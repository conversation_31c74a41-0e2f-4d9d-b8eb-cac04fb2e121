# 店铺分析数据模型

from sqlalchemy import Column, Integer, String, DECIMAL, SmallInteger, TIMESTAMP, Index, Date
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional
from db.connection import Base

class ShopAnalysis(Base):
    """店铺每日分析数据SQLAlchemy模型"""
    __tablename__ = 'shop_analysis'
    
    # 字段定义
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(Integer, nullable=False, comment='店铺ID')
    shop_name = Column(String(255), nullable=False, comment='店铺名称')
    analysis_date = Column(Date, nullable=False, comment='分析日期')
    
    # 营业数据
    total_revenue = Column(DECIMAL(12, 2), nullable=False, default=0, comment='总营业额(线上+线下+耗卡)')
    total_cash_flow = Column(DECIMAL(12, 2), nullable=False, default=0, comment='总现金流(线上+线下+卡金)')
    online_pay_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='线上支付金额')
    offline_pay_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='线下支付金额')
    cost_card_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='耗卡金额')
    
    # 卡金数据
    all_card_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='总卡金金额')
    card_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='办卡金额')
    recharge_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='充值金额')
    
    # 平台数据
    mt_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='美团金额')
    dy_amount = Column(DECIMAL(12, 2), nullable=False, default=0, comment='抖音金额')
    
    # 提成数据
    total_commission = Column(DECIMAL(12, 2), nullable=False, default=0, comment='总提成金额')
    sales_commission = Column(DECIMAL(12, 2), nullable=False, default=0, comment='销售提成金额')
    service_commission = Column(DECIMAL(12, 2), nullable=False, default=0, comment='技师提成金额')
    
    # 流量数据
    all_exposure_cnt = Column(Integer, nullable=False, default=0, comment='总曝光人数')
    mt_exposure_cnt = Column(Integer, nullable=False, default=0, comment='美团曝光人数')
    dp_exposure_cnt = Column(Integer, nullable=False, default=0, comment='点评曝光人数')
    all_visit_cnt = Column(Integer, nullable=False, default=0, comment='总访问人数')
    mt_visit_cnt = Column(Integer, nullable=False, default=0, comment='美团访问人数')
    dp_visit_cnt = Column(Integer, nullable=False, default=0, comment='点评访问人数')
    all_intent_cnt = Column(Integer, nullable=False, default=0, comment='总意向转化人数')
    mt_intent_cnt = Column(Integer, nullable=False, default=0, comment='美团意向转化人数')
    dp_intent_cnt = Column(Integer, nullable=False, default=0, comment='点评意向转化人数')
    flow_cnt = Column(Integer, nullable=False, default=0, comment='客流量')
    
    # 评价数据
    good_review_new_cnt = Column(Integer, nullable=False, default=0, comment='新增好评数')
    bad_review_new_cnt = Column(Integer, nullable=False, default=0, comment='新增差评数')
    mt_star = Column(DECIMAL(3, 1), nullable=False, default=0, comment='美团星级')
    dp_star = Column(DECIMAL(3, 1), nullable=False, default=0, comment='点评星级')
    
    # 券数据
    order_coupon_cnt = Column(Integer, nullable=False, default=0, comment='下单券数')
    verify_coupon_cnt = Column(Integer, nullable=False, default=0, comment='核销券数')
    refund_coupon_cnt = Column(Integer, nullable=False, default=0, comment='退款券数')
    
    # 员工数据
    working_count = Column(Integer, nullable=False, default=0, comment='上班人数')
    
    # 系统字段
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')
    
    # 索引定义
    __table_args__ = (
        Index('idx_shop_analysis_shop_date', 'shop_id', 'analysis_date'),
        Index('idx_shop_analysis_date', 'analysis_date'),
        Index('idx_shop_analysis_shop_id', 'shop_id'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )