# 定时任务调度器

import threading
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from config import get_logger, setup_logging
from service.shop import sync_data, analyze_shop_data

logger = get_logger(__name__)

class DataSyncScheduler:
    """数据同步定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': ThreadPoolExecutor(max_workers=2)
        }
        job_defaults = {
            'coalesce': False,  # 不合并任务
            'max_instances': 1,  # 最大实例数
            'misfire_grace_time': 30  # 错过执行时间的宽限期（秒）
        }
        
        # 使用BackgroundScheduler在后台运行
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        self.is_running = False
        
    def _setup_task_logging(self):
        """为任务线程设置日志配置"""
        try:
            setup_logging()
        except Exception as e:
            print(f"设置任务日志配置失败: {e}")
        
    def sync_yesterday_data(self):
        """同步昨天的数据"""
        # 确保日志配置正确
        self._setup_task_logging()
        task_logger = get_logger(f"{__name__}.sync_yesterday")
        
        try:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            task_logger.info(f"🔄 [定时任务] 开始同步昨天的数据: {yesterday}")
            print(f"🔄 [定时任务] 开始同步昨天的数据: {yesterday}")
            
            # 执行数据同步
            sync_data(yesterday, False)
            task_logger.info(f"✅ [定时任务] 昨天数据同步完成: {yesterday}")
            print(f"✅ [定时任务] 昨天数据同步完成: {yesterday}")
            
            # 执行数据分析
            analyze_shop_data(yesterday)
            task_logger.info(f"📊 [定时任务] 昨天数据分析完成: {yesterday}")
            print(f"📊 [定时任务] 昨天数据分析完成: {yesterday}")
            
        except Exception as e:
            task_logger.error(f"❌ [定时任务] 同步昨天数据失败: {str(e)}")
            print(f"❌ [定时任务] 同步昨天数据失败: {str(e)}")
    
    def sync_current_data(self):
        """同步当前数据（仅订单数据）"""
        # 确保日志配置正确
        self._setup_task_logging()
        task_logger = get_logger(f"{__name__}.sync_current")
        
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            task_logger.info(f"🔄 [定时任务] 开始同步今天的数据: {today}")
            print(f"🔄 [定时任务] 开始同步今天的数据: {today}")
            
            # 只同步今天的数据（美团数据无法获取当天的，所以isToday=True）
            sync_data(today, True)
            task_logger.info(f"✅ [定时任务] 今天数据同步完成: {today}")
            print(f"✅ [定时任务] 今天数据同步完成: {today}")
            
            # 分析今天的数据
            analyze_shop_data(today)
            task_logger.info(f"📊 [定时任务] 今天数据分析完成: {today}")
            print(f"📊 [定时任务] 今天数据分析完成: {today}")
            
        except Exception as e:
            task_logger.error(f"❌ [定时任务] 同步今天数据失败: {str(e)}")
            print(f"❌ [定时任务] 同步今天数据失败: {str(e)}")
    
    def test_task(self):
        """测试任务 - 用于验证定时任务是否正常工作"""
        self._setup_task_logging()
        task_logger = get_logger(f"{__name__}.test")
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        task_logger.info(f"🧪 [测试任务] 定时任务正常运行 - {current_time}")
        print(f"🧪 [测试任务] 定时任务正常运行 - {current_time}")
    
    def add_sync_jobs(self):
        """添加同步任务"""
        # 添加测试任务，每分钟执行一次，用于验证定时任务是否正常
        self.scheduler.add_job(
            func=self.test_task,
            trigger=IntervalTrigger(minutes=1),
            id='test_task',
            name='测试任务',
            replace_existing=True
        )
        
        # 每天凌晨1点同步昨天的完整数据
        self.scheduler.add_job(
            func=self.sync_yesterday_data,
            trigger=CronTrigger(hour=1, minute=0),
            id='sync_yesterday_data',
            name='同步昨天完整数据',
            replace_existing=True
        )
        
        # 每10分钟同步一次当前数据（主要是订单数据）
        self.scheduler.add_job(
            func=self.sync_current_data,
            trigger=IntervalTrigger(minutes=10),
            id='sync_current_data',
            name='同步当前数据',
            replace_existing=True
        )
        
        logger.info("🕐 定时同步任务已添加")
        print("🕐 定时同步任务已添加")
    
    def start(self):
        """启动调度器"""
        if not self.is_running:
            self.add_sync_jobs()
            self.scheduler.start()
            self.is_running = True
            logger.info("🕐 定时任务调度器已启动")
            print("🕐 定时任务调度器已启动")
        else:
            logger.warning("定时任务调度器已经在运行中")
    
    def stop(self):
        """停止调度器"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("定时任务调度器已停止")
            print("定时任务调度器已停止")
    
    def pause_job(self, job_id: str):
        """暂停指定任务"""
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"任务 {job_id} 已暂停")
        except Exception as e:
            logger.error(f"暂停任务 {job_id} 失败: {str(e)}")
    
    def resume_job(self, job_id: str):
        """恢复指定任务"""
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"任务 {job_id} 已恢复")
        except Exception as e:
            logger.error(f"恢复任务 {job_id} 失败: {str(e)}")
    
    def modify_job_interval(self, job_id: str, minutes: int):
        """修改任务执行间隔"""
        try:
            self.scheduler.modify_job(job_id, trigger=IntervalTrigger(minutes=minutes))
            logger.info(f"任务 {job_id} 执行间隔已修改为 {minutes} 分钟")
        except Exception as e:
            logger.error(f"修改任务 {job_id} 间隔失败: {str(e)}")
    
    def get_jobs(self):
        """获取所有任务"""
        return self.scheduler.get_jobs()
    
    def get_job(self, job_id: str):
        """获取指定任务"""
        return self.scheduler.get_job(job_id)

# 全局调度器实例
data_sync_scheduler = DataSyncScheduler()