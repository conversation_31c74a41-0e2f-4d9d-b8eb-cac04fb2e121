from fastapi import APIRouter, HTTPException, Query
from typing import Optional
from datetime import date, timedelta
from config import get_logger
from db.shop_analysis.dao import shop_analysis_dao
from db.shop.dao import shop_dao

logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/analysis", tags=["和分析"])


@router.get("/summary")
async def get_analysis_summary(
    shop_id: Optional[int] = Query(None, description="店铺ID，不传则查询所有店铺"),
    time_range: str = Query("today", description="时间范围：today, yesterday, week, month")
):
    """
    获取分析汇总数据
    """
    try:
        # 计算日期范围
        today = date.today()
        if time_range == "today":
            start_date = end_date = today
        elif time_range == "yesterday":
            start_date = end_date = today - timedelta(days=1)
        elif time_range == "week":
            start_date = today - timedelta(days=6)
            end_date = today
        elif time_range == "month":
            start_date = today - timedelta(days=29)
            end_date = today
        else:
            raise HTTPException(status_code=400, detail="无效的时间范围")
        
        # 查询数据
        analyses = shop_analysis_dao.get_analysis_by_date_range(start_date, end_date, shop_id)
        
        if not analyses:
            # 返回默认数据
            return {
                "metrics": {
                    "totalRevenue": {"value": 0, "change": 0},
                    "totalCashFlow": {"value": 0, "change": 0},
                    "totalTraffic": {"value": 0, "change": 0},
                    "totalCardConsumption": {"value": 0, "change": 0},
                    "totalMeituan": {"value": 0, "change": 0},
                    "totalDouyin": {"value": 0, "change": 0},
                    "totalOnline": {"value": 0, "change": 0},
                    "totalOffline": {"value": 0, "change": 0},
                    "totalCard": {"value": 0, "change": 0},
                    "totalReviews": {"value": 0, "change": 0}
                }
            }
        
        # 计算汇总数据
        total_revenue = sum(float(a.total_revenue or 0) for a in analyses)
        total_cash_flow = sum(float(a.total_cash_flow or 0) for a in analyses)
        total_traffic = sum(a.flow_cnt or 0 for a in analyses)
        total_card_consumption = sum(float(a.cost_card_amount or 0) for a in analyses)
        total_meituan = sum(float(a.mt_amount or 0) for a in analyses)
        total_douyin = sum(float(a.dy_amount or 0) for a in analyses)
        total_online = sum(float(a.online_pay_amount or 0) for a in analyses)
        total_offline = sum(float(a.offline_pay_amount or 0) for a in analyses)
        total_card = sum(float(a.all_card_amount or 0) for a in analyses)
        total_reviews = sum(a.good_review_new_cnt or 0 for a in analyses)
        
        # 计算对比周期的日期范围
        if time_range == "today":
            # 今天对比昨天
            compare_start_date = compare_end_date = start_date - timedelta(days=1)
        elif time_range == "yesterday":
            # 昨天对比前天
            compare_start_date = compare_end_date = start_date - timedelta(days=1)
        elif time_range == "week":
            # 本周对比上周（7天前的7天）
            compare_end_date = start_date - timedelta(days=1)
            compare_start_date = compare_end_date - timedelta(days=6)
        elif time_range == "month":
            # 本月对比上月（30天前的30天）
            compare_end_date = start_date - timedelta(days=1)
            compare_start_date = compare_end_date - timedelta(days=29)
        
        # 查询对比周期的数据
        compare_data = shop_analysis_dao.get_analysis_by_date_range(compare_start_date, compare_end_date, shop_id)
        
        def calculate_change_rate(current_value, previous_value):
            """计算变化率"""
            if previous_value == 0:
                return 0.0 if current_value == 0 else 100.0
            return round(((current_value - previous_value) / previous_value) * 100, 1)
        
        # 如果有对比周期的数据，计算真实变化率
        if compare_data:
            compare_total_revenue = sum(float(a.total_revenue or 0) for a in compare_data)
            compare_total_cash_flow = sum(float(a.total_cash_flow or 0) for a in compare_data)
            compare_total_traffic = sum(a.flow_cnt or 0 for a in compare_data)
            compare_total_card_consumption = sum(float(a.cost_card_amount or 0) for a in compare_data)
            compare_total_meituan = sum(float(a.mt_amount or 0) for a in compare_data)
            compare_total_douyin = sum(float(a.dy_amount or 0) for a in compare_data)
            compare_total_online = sum(float(a.online_pay_amount or 0) for a in compare_data)
            compare_total_offline = sum(float(a.offline_pay_amount or 0) for a in compare_data)
            compare_total_card = sum(float(a.all_card_amount or 0) for a in compare_data)
            compare_total_reviews = sum(a.good_review_new_cnt or 0 for a in compare_data)
            
            revenue_change = calculate_change_rate(total_revenue, compare_total_revenue)
            cash_flow_change = calculate_change_rate(total_cash_flow, compare_total_cash_flow)
            traffic_change = calculate_change_rate(total_traffic, compare_total_traffic)
            card_consumption_change = calculate_change_rate(total_card_consumption, compare_total_card_consumption)
            meituan_change = calculate_change_rate(total_meituan, compare_total_meituan)
            douyin_change = calculate_change_rate(total_douyin, compare_total_douyin)
            online_change = calculate_change_rate(total_online, compare_total_online)
            offline_change = calculate_change_rate(total_offline, compare_total_offline)
            card_change = calculate_change_rate(total_card, compare_total_card)
            reviews_change = calculate_change_rate(total_reviews, compare_total_reviews)
        else:
            # 如果没有对比周期的数据，变化率为0
            revenue_change = cash_flow_change = traffic_change = card_consumption_change = 0.0
            meituan_change = douyin_change = online_change = offline_change = card_change = reviews_change = 0.0
        
        return {
            "metrics": {
                "totalRevenue": {"value": total_revenue, "change": revenue_change},
                "totalCashFlow": {"value": total_cash_flow, "change": cash_flow_change},
                "totalTraffic": {"value": total_traffic, "change": traffic_change},
                "totalCardConsumption": {"value": total_card_consumption, "change": card_consumption_change},
                "totalMeituan": {"value": total_meituan, "change": meituan_change},
                "totalDouyin": {"value": total_douyin, "change": douyin_change},
                "totalOnline": {"value": total_online, "change": online_change},
                "totalOffline": {"value": total_offline, "change": offline_change},
                "totalCard": {"value": total_card, "change": card_change},
                "totalReviews": {"value": total_reviews, "change": reviews_change}
            }
        }
        
    except Exception as e:
        logger.error(f"获取分析汇总数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取分析汇总数据失败")

@router.get("/rankings")
async def get_rankings(
    ranking_type: str = Query("revenue", description="排行榜类型：revenue, traffic, staff, commission"),
    time_range: str = Query("today", description="时间范围：today, yesterday, week, month")
):
    """
    获取排行榜数据
    """
    try:
        # 计算日期范围
        today = date.today()
        if time_range == "today":
            start_date = end_date = today
        elif time_range == "yesterday":
            start_date = end_date = today - timedelta(days=1)
        elif time_range == "week":
            start_date = today - timedelta(days=6)
            end_date = today
        elif time_range == "month":
            start_date = today - timedelta(days=29)
            end_date = today
        else:
            raise HTTPException(status_code=400, detail="无效的时间范围")
        
        # 查询数据
        analyses = shop_analysis_dao.get_analysis_by_date_range(start_date, end_date)
        
        if not analyses:
            return {"rankings": []}
        
        # 按店铺聚合数据，同时记录shop_id
        shop_data = {}
        shop_ids = {}  # 记录店铺名称对应的shop_id
        for analysis in analyses:
            shop_name = analysis.shop_name
            shop_id = analysis.shop_id
            
            if shop_name not in shop_data:
                shop_data[shop_name] = {
                    "revenue": 0,
                    "traffic": 0,
                    "staff": 0,
                    "commission": 0
                }
                shop_ids[shop_name] = shop_id
            
            shop_data[shop_name]["revenue"] += float(analysis.total_revenue or 0)
            shop_data[shop_name]["traffic"] += analysis.flow_cnt or 0
            shop_data[shop_name]["staff"] += analysis.working_count or 0
            shop_data[shop_name]["commission"] += float(analysis.total_commission or 0)
        
        # 根据排行榜类型排序
        if ranking_type == "revenue":
            sorted_shops = sorted(shop_data.items(), key=lambda x: x[1]["revenue"], reverse=True)
            max_value = max(shop_data.values(), key=lambda x: x["revenue"])["revenue"] if shop_data else 1
        elif ranking_type == "traffic":
            sorted_shops = sorted(shop_data.items(), key=lambda x: x[1]["traffic"], reverse=True)
            max_value = max(shop_data.values(), key=lambda x: x["traffic"])["traffic"] if shop_data else 1
        elif ranking_type == "staff":
            sorted_shops = sorted(shop_data.items(), key=lambda x: x[1]["staff"], reverse=True)
            max_value = max(shop_data.values(), key=lambda x: x["staff"])["staff"] if shop_data else 1
        elif ranking_type == "commission":
            sorted_shops = sorted(shop_data.items(), key=lambda x: x[1]["commission"], reverse=True)
            max_value = max(shop_data.values(), key=lambda x: x["commission"])["commission"] if shop_data else 1
        else:
            raise HTTPException(status_code=400, detail="无效的排行榜类型")
        
        # 构建排行榜数据，包含经纬度信息
        rankings = []
        for i, (shop_name, data) in enumerate(sorted_shops): 
            value = data[ranking_type]
            progress = (value / max_value * 100) if max_value > 0 else 0
            
            # 获取店铺的经纬度信息
            shop_id = shop_ids.get(shop_name)
            lng = None
            lat = None
            
            if shop_id:
                shop = shop_dao.get_shop_by_id(shop_id)
                # logger.info(f"获取店铺 {shop_name} 的经纬度信息: lng={shop.lng}, lat={shop.lat}")
                if shop:
                    lng = shop.lng
                    lat = shop.lat
            
            rankings.append({
                "name": shop_name,
                "value": value,
                "rank": i + 1,
                "progress": progress,
                "lng": lng,
                "lat": lat,
                "shop_id": shop_id
            })
        
        return {"rankings": rankings}
        
    except Exception as e:
        logger.error(f"获取排行榜数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取排行榜数据失败")

@router.get("/trends")
async def get_trends(
    trend_type: str = Query("revenue", description="趋势类型：revenue, traffic, recharge"),
    shop_id: Optional[int] = Query(None, description="店铺ID，不传则查询所有店铺"),
    days: int = Query(30, description="查询天数，默认30天（一个月）")
):
    """
    获取趋势数据
    """
    try:
        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # 查询数据
        analyses = shop_analysis_dao.get_analysis_by_date_range(start_date, end_date, shop_id)
        
        # 按日期聚合数据
        date_data = {}
        for analysis in analyses:
            analysis_date = analysis.analysis_date.strftime('%Y-%m-%d')
            if analysis_date not in date_data:
                date_data[analysis_date] = {
                    "revenue": 0,
                    "traffic": 0,
                    "recharge": 0
                }
            
            date_data[analysis_date]["revenue"] += float(analysis.total_revenue or 0)
            date_data[analysis_date]["traffic"] += analysis.flow_cnt or 0
            date_data[analysis_date]["recharge"] += float(analysis.recharge_amount or 0)
        
        # 构建趋势数据
        dates = []
        values = []
        
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            dates.append(date_str)
            
            if date_str in date_data:
                values.append(date_data[date_str][trend_type])
            else:
                values.append(0)
            
            current_date += timedelta(days=1)
        
        return {
            "dates": dates,
            "values": values,
            "type": trend_type
        }
        
    except Exception as e:
        logger.error(f"获取趋势数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取趋势数据失败")

@router.get("/trends/data")
async def get_trends_data(
    metric_type: str = Query(..., description="指标类型: revenue, traffic, recharge"),
    shop_id: Optional[int] = Query(None, description="店铺ID，为空则查询所有店铺"),
    days: int = Query(7, description="天数，默认7天")
):
    """获取趋势图数据"""
    try:
        from datetime import datetime, timedelta
        
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 获取数据
        analyses = shop_analysis_dao.get_analysis_by_date_range(start_date, end_date, shop_id)
        
        # 按日期分组数据
        date_data = {}
        for analysis in analyses:
            date_str = analysis.analysis_date.strftime("%m-%d")
            if date_str not in date_data:
                date_data[date_str] = {
                    'revenue': 0,
                    'traffic': 0,
                    'recharge': 0
                }
            
            # 累加数据
            date_data[date_str]['revenue'] += float(analysis.total_revenue or 0)
            date_data[date_str]['traffic'] += analysis.flow_cnt or 0
            date_data[date_str]['recharge'] += float(analysis.recharge_amount or 0)
        
        # 生成完整的日期序列
        dates = []
        values = []
        current_date = start_date
        
        while current_date <= end_date:
            date_str = current_date.strftime("%m-%d")
            dates.append(date_str)
            
            if date_str in date_data:
                values.append(date_data[date_str].get(metric_type, 0))
            else:
                values.append(0)
            
            current_date += timedelta(days=1)
        
        return {
            "dates": dates,
            "values": values,
            "metric_type": metric_type
        }
        
    except Exception as e:
        logger.error(f"获取趋势数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取趋势数据失败: {str(e)}")

@router.get("/stores")
async def get_stores():
    """获取门店选择数据"""
    try:
        from db.shop.dao import shop_dao
        
        # 获取所有门店
        shops = shop_dao.get_all_shops()
        
        stores = [
            {
                "id": "all",
                "name": "全部门店",
                "value": "all"
            }
        ]
        
        for shop in shops:
            stores.append({
                "id": str(shop.shop_id),
                "name": shop.name,
                "value": str(shop.shop_id)
            })
        
        return {
            "stores": stores
        }
        
    except Exception as e:
        logger.error(f"获取门店数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取门店数据失败: {str(e)}")


