<template>
  <div id="app">
    <!-- 标题栏 -->
    <div class="header">
      <h1>方松院连锁门店数据看板</h1>
      <div class="header-controls">
        <div class="datetime">{{ currentDateTime }}</div>
        <div class="control-group">
          <label>门店:</label>
          <select v-model="selectedStore" @change="onStoreChange">
            <option v-for="store in stores" :key="store.id" :value="store.id">
              {{ store.name }}
            </option>
          </select>
        </div>
        <div class="control-group">
          <label>时间:</label>
          <select v-model="selectedTimeRange" @change="onTimeRangeChange">
            <option value="today">今日</option>
            <option value="yesterday">昨日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 上半部分：地图和指标 -->
      <div class="top-section">
        <!-- 地图容器 - 仅在桌面端显示 -->
        <div v-if="!isMobile" class="map-container">
          <div ref="mapChart" class="map-chart"></div>

          <!-- 排行榜容器 - 桌面端绝对定位在地图上方 -->
          <div class="rankings-container">
            <div class="ranking-tabs">
              <div
                v-for="tab in rankingTabs"
                :key="tab.type"
                :class="['ranking-tab', { active: currentRankingType === tab.type }]"
                @click="switchRankingTab(tab.type)"
              >
                {{ tab.label }}
              </div>
            </div>
            <div class="ranking-list">
              <div
                v-for="item in currentRankings"
                :key="item.rank"
                class="ranking-item"
              >
                <div :class="['ranking-number', getRankClass(item.rank)]">
                  {{ item.rank }}
                </div>
                <div class="ranking-info">
                  <div class="ranking-name">{{ item.name }}</div>
                  <div class="ranking-progress">
                    <div
                      :class="['ranking-progress-bar', `type-${currentRankingType}`, `rank-${item.rank}`]"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                  <div class="ranking-value">{{ formatRankingValue(item.value, currentRankingType) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 指标卡片容器 -->
        <div class="metrics-container">
          <div
            v-for="metric in metrics"
            :key="metric.key"
            :data-metric-key="metric.key"
            class="metric-card"
          >
            <div class="metric-label">{{ metric.label }}</div>
            <div
              class="metric-value"
              :data-metric-value="metric.key"
            >{{ metric.formattedValue }}</div>
            <div :class="['metric-change', metric.change > 0 ? 'positive' : 'negative']">
              <span class="change-arrow">{{ metric.change > 0 ? '↗' : '↘' }}</span>
              <span>{{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端排行榜容器 - 仅在移动端显示 -->
      <div v-if="isMobile" class="rankings-container mobile-rankings">
        <div class="ranking-tabs">
          <div
            v-for="tab in rankingTabs"
            :key="tab.type"
            :class="['ranking-tab', { active: currentRankingType === tab.type }]"
            @click="switchRankingTab(tab.type)"
          >
            {{ tab.label }}
          </div>
        </div>
        <div class="ranking-list">
          <div
            v-for="item in currentRankings"
            :key="item.rank"
            class="ranking-item"
          >
            <div :class="['ranking-number', getRankClass(item.rank)]">
              {{ item.rank }}
            </div>
            <div class="ranking-info">
              <div class="ranking-name">{{ item.name }}</div>
              <div class="ranking-progress">
                <div
                  :class="['ranking-progress-bar', `type-${currentRankingType}`, `rank-${item.rank}`]"
                  :style="{ width: item.percentage + '%' }"
                ></div>
              </div>
              <div class="ranking-value">{{ formatRankingValue(item.value, currentRankingType) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 下半部分：趋势图表 -->
      <div class="bottom-section">
        <div class="chart-container">
          <div class="chart-title">总营业额趋势</div>
          <div ref="revenueChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <div class="chart-title">总客流趋势</div>
          <div ref="trafficChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <div class="chart-title">总充值金额趋势</div>
          <div ref="rechargeChart" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { apiService } from './services/api.js'
import { mockData } from './services/mockData.js'
import changshaGeoJson from './assets/changsha.json'
import wuhanGeoJson from './assets/wuhan.json'

export default {
  name: 'App',
  setup() {
    // 响应式数据
    const currentDateTime = ref('')
    const selectedStore = ref('all')
    const selectedTimeRange = ref('today')
    const currentRankingType = ref('revenue')
    const windowWidth = ref(window.innerWidth)
    
    // 图表实例
    const mapChart = ref(null)
    const revenueChart = ref(null)
    const trafficChart = ref(null)
    const rechargeChart = ref(null)
    
    // 图表对象
    let mapChartInstance = null
    let revenueChartInstance = null
    let trafficChartInstance = null
    let rechargeChartInstance = null
    
    // 定时器
    let dateTimeTimer = null
    let autoSwitchTimer = null
    
    // 数据
    const stores = ref([
      { id: 'all', name: '全部门店' }
    ])

    const rankingTabs = [
      { type: 'revenue', label: '营业额' },
      { type: 'traffic', label: '客流' },
      { type: 'staff', label: '上班人数' },
      { type: 'commission', label: '提成' }
    ]

    const metrics = ref([])
    const currentRankings = ref([])

    // 数据缓存
    const dataCache = ref({
      metrics: null,
      rankings: null,
      trends: null,
      mapData: null,
      lastUpdate: null
    })

    // 动画状态
    const animationStates = ref({})

    // 计算属性
    const isMobile = computed(() => {
      return windowWidth.value <= 1023
    })
    
    // 工具函数
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    }

    // 数据比较函数
    const compareMetricsData = (newData, cachedData) => {
      if (!cachedData) return { hasChanges: true, changes: {} }

      const changes = {}
      let hasChanges = false

      newData.forEach(newMetric => {
        const cachedMetric = cachedData.find(m => m.key === newMetric.key)
        if (cachedMetric) {
          if (newMetric.value !== cachedMetric.value) {
            changes[newMetric.key] = {
              oldValue: cachedMetric.value,
              newValue: newMetric.value,
              type: newMetric.value > cachedMetric.value ? 'increase' : 'decrease'
            }
            hasChanges = true
          }
        } else {
          changes[newMetric.key] = {
            oldValue: 0,
            newValue: newMetric.value,
            type: 'new'
          }
          hasChanges = true
        }
      })

      return { hasChanges, changes }
    }

    // 动画触发函数
    const triggerMetricAnimation = (metricKey, animationType) => {
      animationStates.value[metricKey] = animationType

      // 在DOM中查找对应的元素并添加CSS类
      nextTick(() => {
        const metricCard = document.querySelector(`[data-metric-key="${metricKey}"]`)
        const metricValue = document.querySelector(`[data-metric-value="${metricKey}"]`)

        if (metricCard) {
          metricCard.classList.remove('data-changed', 'data-increased', 'data-decreased')
          metricCard.classList.add(`data-${animationType}`)

          // 移除动画类
          setTimeout(() => {
            metricCard.classList.remove(`data-${animationType}`)
          }, 1500)
        }

        if (metricValue) {
          metricValue.classList.remove('value-changing', 'value-increased', 'value-decreased')
          metricValue.classList.add(`value-${animationType}`)

          // 移除动画类
          setTimeout(() => {
            metricValue.classList.remove(`value-${animationType}`)
          }, 800)
        }
      })
    }

    // 坐标验证和处理函数
    const validateCoordinates = (lng, lat) => {
      // 检查坐标是否为有效数字
      const longitude = parseFloat(lng)
      const latitude = parseFloat(lat)

      if (isNaN(longitude) || isNaN(latitude)) {
        return null
      }

      // 检查坐标范围是否合理（中国境内大致范围）
      if (longitude < 70 || longitude > 140 || latitude < 10 || latitude > 55) {
        return null
      }

      return [longitude, latitude]
    }

    // 处理排行榜数据并提取坐标信息
    const processRankingsWithCoordinates = (rankings, type) => {
      const processedStores = []

      console.log(`Processing ${rankings.length} rankings for type: ${type}`)

      rankings.forEach((ranking, index) => {
        // 提取坐标信息
        const coordinates = validateCoordinates(ranking.lng, ranking.lat)

        if (coordinates) {
          // 创建地图数据格式
          const storeData = {
            name: ranking.name,
            coord: coordinates,
            value: ranking.value,
            rank: ranking.rank,
            // 根据排行榜类型设置对应的数值
            revenue: type === 'revenue' ? ranking.value : (ranking.revenue || ranking.value),
            traffic: type === 'traffic' ? ranking.value : (ranking.traffic || 0),
            staff: type === 'staff' ? ranking.value : (ranking.staff || 0),
            commission: type === 'commission' ? ranking.value : (ranking.commission || 0),
            reviews: ranking.reviews || 4.5
          }
          processedStores.push(storeData)

          if (index < 3) { // Log first 3 stores for debugging
            console.log(`Store ${index + 1}: ${ranking.name} at [${coordinates[0]}, ${coordinates[1]}] with value ${ranking.value}`)
          }
        } else {
          console.warn(`Invalid coordinates for store: ${ranking.name}, lng: ${ranking.lng}, lat: ${ranking.lat}`)
        }
      })

      console.log(`Successfully processed ${processedStores.length} stores with valid coordinates`)
      return processedStores
    }
    
    const formatCurrency = (num) => {
      if (num >= 10000) {
        return '¥' + (num / 10000).toFixed(1) + '万'
      }
      return '¥' + num.toLocaleString()
    }
    
    const formatRankingValue = (value, type) => {
      if (type === 'revenue' || type === 'commission') {
        return formatCurrency(value)
      } else if (type === 'traffic' || type === 'staff') {
        return formatNumber(value) + '人'
      }
      return value
    }
    
    const getRankClass = (rank) => {
      if (rank === 1) return 'first'
      if (rank === 2) return 'second'
      if (rank === 3) return 'third'
      return 'other'
    }
    
    // 更新日期时间
    const updateDateTime = () => {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }
      currentDateTime.value = now.toLocaleDateString('zh-CN', options)
    }
    
    // 获取门店数据
    const loadStores = async () => {
      try {
        const storesData = await apiService.fetchStores()
        stores.value = storesData
        console.log('门店数据加载成功:', storesData)
      } catch (error) {
        console.error('获取门店数据失败:', error)
        // 保持默认的"全部门店"选项
        stores.value = [
          { id: 'all', name: '全部门店' }
        ]
      }
    }
    
    // 更新指标数据
    const updateMetrics = async () => {
      try {
        const storeId = selectedStore.value === 'all' ? null : selectedStore.value
        const data = await apiService.fetchAnalysisSummary(storeId, selectedTimeRange.value)

        const metricsData = data.metrics
        const newMetrics = [
          {
            key: 'totalRevenue',
            label: '总营业额',
            value: metricsData.totalRevenue?.value || 0,
            change: metricsData.totalRevenue?.change || 0,
            formattedValue: formatCurrency(metricsData.totalRevenue?.value || 0)
          },
          {
            key: 'totalCashFlow',
            label: '总现金流',
            value: metricsData.totalCashFlow?.value || 0,
            change: metricsData.totalCashFlow?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCashFlow?.value || 0)
          },
          {
            key: 'totalTraffic',
            label: '总客流',
            value: metricsData.totalTraffic?.value || 0,
            change: metricsData.totalTraffic?.change || 0,
            formattedValue: formatNumber(metricsData.totalTraffic?.value || 0) + '人'
          },
          {
            key: 'totalCardConsumption',
            label: '总耗卡金额',
            value: metricsData.totalCardConsumption?.value || 0,
            change: metricsData.totalCardConsumption?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCardConsumption?.value || 0)
          },
          {
            key: 'totalMeituan',
            label: '总美团金额',
            value: metricsData.totalMeituan?.value || 0,
            change: metricsData.totalMeituan?.change || 0,
            formattedValue: formatCurrency(metricsData.totalMeituan?.value || 0)
          },
          {
            key: 'totalDouyin',
            label: '总抖音金额',
            value: metricsData.totalDouyin?.value || 0,
            change: metricsData.totalDouyin?.change || 0,
            formattedValue: formatCurrency(metricsData.totalDouyin?.value || 0)
          },
          {
            key: 'totalOnline',
            label: '总线上消费金额',
            value: metricsData.totalOnline?.value || 0,
            change: metricsData.totalOnline?.change || 0,
            formattedValue: formatCurrency(metricsData.totalOnline?.value || 0)
          },
          {
            key: 'totalOffline',
            label: '总线下消费金额',
            value: metricsData.totalOffline?.value || 0,
            change: metricsData.totalOffline?.change || 0,
            formattedValue: formatCurrency(metricsData.totalOffline?.value || 0)
          },
          {
            key: 'totalCard',
            label: '总卡金额',
            value: metricsData.totalCard?.value || 0,
            change: metricsData.totalCard?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCard?.value || 0)
          },
          {
            key: 'totalReviews',
            label: '好评总量',
            value: metricsData.totalReviews?.value || 0,
            change: metricsData.totalReviews?.change || 0,
            formattedValue: formatNumber(metricsData.totalReviews?.value || 0) + '条'
          }
        ]

        // 比较数据变化并触发动画
        const comparison = compareMetricsData(newMetrics, dataCache.value.metrics)

        if (comparison.hasChanges) {
          // 触发动画效果
          Object.keys(comparison.changes).forEach(metricKey => {
            const change = comparison.changes[metricKey]
            if (change.type === 'increase') {
              triggerMetricAnimation(metricKey, 'increased')
            } else if (change.type === 'decrease') {
              triggerMetricAnimation(metricKey, 'decreased')
            } else {
              triggerMetricAnimation(metricKey, 'changed')
            }
          })

          // 更新缓存
          dataCache.value.metrics = [...newMetrics]
          dataCache.value.lastUpdate = Date.now()
        }

        // 更新显示数据
        metrics.value = newMetrics

      } catch (error) {
        console.error('更新指标数据失败:', error)
        // 使用模拟数据
        const mockMetrics = mockData.getAnalysisSummary().metrics
        const fallbackMetrics = [
          {
            key: 'totalRevenue',
            label: '总营业额',
            value: mockMetrics.totalRevenue.value,
            change: mockMetrics.totalRevenue.change,
            formattedValue: formatCurrency(mockMetrics.totalRevenue.value)
          }
          // ... 其他指标
        ]

        // 即使是模拟数据也要检查变化
        const comparison = compareMetricsData(fallbackMetrics, dataCache.value.metrics)
        if (comparison.hasChanges) {
          dataCache.value.metrics = [...fallbackMetrics]
        }

        metrics.value = fallbackMetrics
      }
    }
    
    // 更新排行榜
    const updateRankings = async (type = 'revenue') => {
      try {
        const data = await apiService.fetchRankings(type, selectedTimeRange.value)
        let rankings = data.rankings

        if (!rankings || rankings.length === 0) {
          rankings = mockData.getRankings(type).rankings || []
        }

        // 检查排行榜数据是否有变化
        const cacheKey = `${type}_${selectedTimeRange.value}`
        const cachedRankings = dataCache.value.rankings?.[cacheKey]

        const rankingsChanged = !cachedRankings ||
          JSON.stringify(rankings) !== JSON.stringify(cachedRankings)

        if (rankingsChanged) {
          // 计算百分比
          const maxValue = Math.max(...rankings.map(r => r.value))
          const processedRankings = rankings.map(item => ({
            ...item,
            percentage: (item.value / maxValue) * 100
          }))

          currentRankings.value = processedRankings

          // 处理坐标数据用于地图显示
          const mapStores = processRankingsWithCoordinates(rankings, type)

          // 更新缓存
          if (!dataCache.value.rankings) {
            dataCache.value.rankings = {}
          }
          dataCache.value.rankings[cacheKey] = [...rankings]

          // 缓存地图数据
          const mapCacheKey = `map_${type}_${selectedTimeRange.value}`
          if (!dataCache.value.mapData) {
            dataCache.value.mapData = {}
          }
          dataCache.value.mapData[mapCacheKey] = {
            stores: mapStores,
            timestamp: Date.now()
          }

          // 只有数据变化时才更新地图
          updateMapData(type, mapStores)
        }

      } catch (error) {
        console.error('更新排行榜失败:', error)
        const rankings = mockData.getRankings(type).rankings || []
        const maxValue = Math.max(...rankings.map(r => r.value))
        currentRankings.value = rankings.map(item => ({
          ...item,
          percentage: (item.value / maxValue) * 100
        }))

        // 使用模拟数据更新地图（fallback）
        const fallbackMapData = mockData.getMapData()
        updateMapData(type, fallbackMapData.stores)
      }
    }
    
    // 更新地图数据
    const updateMapData = (type = 'revenue', storeData = null) => {
      if (!mapChartInstance) return

      let stores = storeData

      // 如果没有提供数据，尝试从缓存获取或使用模拟数据
      if (!stores) {
        const mapCacheKey = `map_${type}_${selectedTimeRange.value}`
        const cachedMapData = dataCache.value.mapData?.[mapCacheKey]

        if (cachedMapData && cachedMapData.stores) {
          stores = cachedMapData.stores
        } else {
          // 使用模拟数据作为fallback
          console.warn('No real coordinate data available, using mock data')
          const mockMapData = mockData.getMapData()
          stores = mockMapData.stores
        }
      }

      // 根据坐标范围分组门店（长沙/武汉区域）
      const changshaStores = stores.filter(store => {
        const [lng, lat] = store.coord
        // 长沙大致坐标范围：经度112.5-113.5，纬度27.8-28.5
        return lng >= 112.5 && lng <= 113.5 && lat >= 27.8 && lat <= 28.5
      })

      const wuhanStores = stores.filter(store => {
        const [lng, lat] = store.coord
        // 武汉大致坐标范围：经度113.8-115.0，纬度30.2-31.0
        return lng >= 113.8 && lng <= 115.0 && lat >= 30.2 && lat <= 31.0
      })

      // 其他区域的门店（如果有的话）
      const otherStores = stores.filter(store => {
        const [lng, lat] = store.coord
        const isChangsha = lng >= 112.5 && lng <= 113.5 && lat >= 27.8 && lat <= 28.5
        const isWuhan = lng >= 113.8 && lng <= 115.0 && lat >= 30.2 && lat <= 31.0
        return !isChangsha && !isWuhan
      })

      const option = mapChartInstance.getOption()

      // 获取门店对应类型的数值
      const getStoreValue = (store, type) => {
        switch(type) {
          case 'revenue': return store.revenue || store.value
          case 'traffic': return store.traffic || store.value
          case 'staff': return store.staff || store.value
          case 'commission': return store.commission || store.value
          case 'reviews': return store.reviews || 4.5
          default: return store.value
        }
      }

      // 计算所有门店的数值范围用于颜色映射
      const allValues = stores.map(store => getStoreValue(store, type))
      const minValue = Math.min(...allValues)
      const maxValue = Math.max(...allValues)

      // 生成颜色图例数据
      const legendData = generateColorLegend(minValue, maxValue, type)

      // 计算最优缩放级别
      const changshaZoom = calculateOptimalZoom(changshaStores)
      const wuhanZoom = calculateOptimalZoom(wuhanStores)

      // 更新长沙区域数据
      if (option.series && option.series[0]) {
        option.series[0].data = changshaStores.map(store => {
          const value = getStoreValue(store, type)
          const color = getValueBasedColor(value, minValue, maxValue)
          return {
            name: store.name,
            value: store.coord.concat(value),
            color: color,
            shadowColor: color.replace('0.8)', '0.6)'),
            itemStyle: {
              color: color,
              shadowColor: color.replace('0.8)', '0.6)')
            }
          }
        })
      }

      // 更新武汉区域数据
      if (option.series && option.series[1]) {
        option.series[1].data = wuhanStores.map(store => {
          const value = getStoreValue(store, type)
          const color = getValueBasedColor(value, minValue, maxValue)
          return {
            name: store.name,
            value: store.coord.concat(value),
            color: color,
            shadowColor: color.replace('0.8)', '0.6)'),
            itemStyle: {
              color: color,
              shadowColor: color.replace('0.8)', '0.6)')
            }
          }
        })
      }

      // 如果有其他区域的门店，添加到第一个系列中（或创建新系列）
      if (otherStores.length > 0) {
        console.log(`Found ${otherStores.length} stores in other regions:`, otherStores.map(s => s.name))
        // 将其他区域门店添加到长沙系列中显示
        if (option.series && option.series[0]) {
          const otherStoreData = otherStores.map(store => {
            const value = getStoreValue(store, type)
            const color = getValueBasedColor(value, minValue, maxValue)
            return {
              name: store.name,
              value: store.coord.concat(value),
              color: color,
              shadowColor: color.replace('0.8)', '0.6)'),
              itemStyle: {
                color: color,
                shadowColor: color.replace('0.8)', '0.6)')
              }
            }
          })
          option.series[0].data = [...option.series[0].data, ...otherStoreData]
        }
      }

      // 更新颜色图例
      if (option.graphic && option.graphic[0] && option.graphic[0].children) {
        const children = option.graphic[0].children
        // 更新最大值颜色块 (index 4)
        if (children[4]) children[4].style.fill = legendData.maxColor
        // 更新最大值数值文本 (index 6)
        if (children[6]) children[6].style.text = legendData.maxLabel
        // 更新最小值颜色块 (index 7)
        if (children[7]) children[7].style.fill = legendData.minColor
        // 更新最小值数值文本 (index 9)
        if (children[9]) children[9].style.text = legendData.minLabel
      }

      // 动态调整地图缩放（如果需要）
      if (option.geo) {
        if (option.geo[0] && changshaStores.length > 0) {
          option.geo[0].zoom = changshaZoom
        }
        if (option.geo[1] && wuhanStores.length > 0) {
          option.geo[1].zoom = wuhanZoom
        }
      }

      mapChartInstance.setOption(option, true)

      console.log(`Map updated with ${stores.length} stores (${changshaStores.length} in Changsha, ${wuhanStores.length} in Wuhan, ${otherStores.length} in other regions)`)
      console.log(`Value range: ${legendData.minLabel} - ${legendData.maxLabel}`)
    }
    
    // 初始化地图
    const initMapChart = async () => {
      if (!mapChart.value) return

      mapChartInstance = echarts.init(mapChart.value)

      try {
        // 注册地图数据
        echarts.registerMap('changsha', changshaGeoJson)
        echarts.registerMap('wuhan', wuhanGeoJson)

        // 使用完整地图配置
        initMapWithGeo()

        // 地图初始化完成后，延迟调整趋势图表大小
        setTimeout(() => {
          if (revenueChartInstance) revenueChartInstance.resize()
          if (trafficChartInstance) trafficChartInstance.resize()
          if (rechargeChartInstance) rechargeChartInstance.resize()
        }, 100)
      } catch (error) {
        console.error('地图数据加载失败:', error)
        // 使用简化地图配置
        initMapWithoutGeo()

        // 简化地图初始化完成后，也调整趋势图表大小
        setTimeout(() => {
          if (revenueChartInstance) revenueChartInstance.resize()
          if (trafficChartInstance) trafficChartInstance.resize()
          if (rechargeChartInstance) rechargeChartInstance.resize()
        }, 100)
      }
    }
    
    // 带地理数据的地图初始化
    const initMapWithGeo = () => {
      const option = {
        backgroundColor: 'transparent',
        // 添加颜色图例
        graphic: [
          {
            type: 'group',
            left: 25,
            top: 25,
            children: [
              // 图例外层阴影
              {
                type: 'rect',
                shape: { x: 2, y: 2, width: 140, height: 90 },
                style: {
                  fill: 'rgba(0, 0, 0, 0.3)',
                  opacity: 0.6
                },
                z: 98
              },
              // 图例背景
              {
                type: 'rect',
                shape: { x: 0, y: 0, width: 140, height: 90 },
                style: {
                  fill: 'linear-gradient(135deg, rgba(0, 30, 60, 0.95), rgba(0, 50, 100, 0.9))',
                  stroke: 'rgba(0, 180, 255, 0.8)',
                  lineWidth: 2,
                  shadowBlur: 8,
                  shadowColor: 'rgba(0, 180, 255, 0.4)'
                },
                z: 99
              },
              // 图例标题背景
              {
                type: 'rect',
                shape: { x: 8, y: 8, width: 124, height: 22 },
                style: {
                  fill: 'rgba(0, 150, 255, 0.2)',
                  stroke: 'rgba(0, 180, 255, 0.5)',
                  lineWidth: 1
                },
                z: 100
              },
              // 图例标题
              {
                type: 'text',
                style: {
                  text: '数值范围',
                  x: 70,
                  y: 24,
                  textAlign: 'center',
                  fill: '#00e4ff',
                  fontSize: 13,
                  fontWeight: 'bold',
                  textShadow: '0 0 8px rgba(0, 228, 255, 0.6)'
                },
                z: 101
              },
              // 最大值颜色块
              {
                type: 'rect',
                shape: { x: 15, y: 38, width: 18, height: 16 },
                style: {
                  fill: 'hsla(120, 80%, 50%, 0.8)',
                  stroke: 'rgba(255, 255, 255, 0.3)',
                  lineWidth: 1
                },
                z: 101
              },
              // 最大值标签
              {
                type: 'text',
                style: {
                  text: 'MAX:',
                  x: 38,
                  y: 48,
                  fill: '#b0e8ff',
                  fontSize: 11,
                  fontWeight: 'bold'
                },
                z: 101
              },
              // 最大值数值
              {
                type: 'text',
                style: {
                  text: '---',
                  x: 65,
                  y: 48,
                  fill: '#ffffff',
                  fontSize: 10,
                  fontWeight: 'normal'
                },
                z: 101
              },
              // 最小值颜色块
              {
                type: 'rect',
                shape: { x: 15, y: 60, width: 18, height: 16 },
                style: {
                  fill: 'hsla(0, 80%, 50%, 0.8)',
                  stroke: 'rgba(255, 255, 255, 0.3)',
                  lineWidth: 1
                },
                z: 101
              },
              // 最小值标签
              {
                type: 'text',
                style: {
                  text: 'MIN:',
                  x: 38,
                  y: 70,
                  fill: '#b0e8ff',
                  fontSize: 11,
                  fontWeight: 'bold'
                },
                z: 101
              },
              // 最小值数值
              {
                type: 'text',
                style: {
                  text: '---',
                  x: 65,
                  y: 70,
                  fill: '#ffffff',
                  fontSize: 10,
                  fontWeight: 'normal'
                },
                z: 101
              }
            ]
          }
        ],
        geo: [
          {
            map: 'changsha',
            roam: true,
            zoom: 2.0, // 增加初始缩放
            center: [113.0823, 28.2070],
            left: '2%',
            right: '53%',
            top: '8%',
            bottom: '8%',
            itemStyle: {
              areaColor: 'rgba(0, 60, 120, 0.4)',
              borderColor: 'rgba(0, 180, 255, 0.6)',
              borderWidth: 1.5
            },
            emphasis: {
              itemStyle: {
                areaColor: 'rgba(0, 180, 255, 0.3)'
              }
            },
            label: {
              show: true,
              color: '#a0d8ff',
              fontSize: 11
            }
          },
          {
            map: 'wuhan',
            roam: true,
            zoom: 2.0, // 增加初始缩放
            center: [114.3054, 30.5931],
            left: '53%',
            right: '2%',
            top: '8%',
            bottom: '8%',
            itemStyle: {
              areaColor: 'rgba(0, 60, 120, 0.4)',
              borderColor: 'rgba(0, 180, 255, 0.6)',
              borderWidth: 1.5
            },
            emphasis: {
              itemStyle: {
                areaColor: 'rgba(0, 180, 255, 0.3)'
              }
            },
            label: {
              show: true,
              color: '#a0d8ff',
              fontSize: 11
            }
          }
        ],
        series: [
          // 长沙热力图
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            data: [], // 初始化为空，将通过updateMapData更新
            symbolSize: function(val) {
              return Math.max(Math.sqrt(val[2] / 10000) + 18, 25)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'fill',
              scale: 1.8,
              period: 3
            },
            itemStyle: {
              color: function(params) {
                // 动态颜色将在updateMapData中设置
                return params.data.color || 'rgba(0, 228, 255, 0.8)'
              },
              shadowBlur: 15,
              shadowColor: function(params) {
                return params.data.shadowColor || 'rgba(0, 228, 255, 0.6)'
              }
            },
            label: {
              show: true,
              position: 'top',
              distance: 8,
              formatter: function(params) {
                return params.name
              },
              color: '#ffffff',
              fontSize: 11,
              fontWeight: 'bold',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              borderColor: 'rgba(0, 150, 255, 0.8)',
              borderWidth: 1,
              borderRadius: 3,
              padding: [2, 4]
            }
          },
          // 武汉热力图
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            geoIndex: 1,
            data: [], // 初始化为空，将通过updateMapData更新
            symbolSize: function(val) {
              return Math.max(Math.sqrt(val[2] / 10000) + 18, 25)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'fill',
              scale: 1.8,
              period: 3
            },
            itemStyle: {
              color: function(params) {
                // 动态颜色将在updateMapData中设置
                return params.data.color || 'rgba(255, 107, 53, 0.8)'
              },
              shadowBlur: 15,
              shadowColor: function(params) {
                return params.data.shadowColor || 'rgba(255, 107, 53, 0.6)'
              }
            },
            label: {
              show: true,
              position: 'top',
              distance: 8,
              formatter: function(params) {
                return params.name
              },
              color: '#ffffff',
              fontSize: 11,
              fontWeight: 'bold',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              borderColor: 'rgba(255, 107, 53, 0.8)',
              borderWidth: 1,
              borderRadius: 3,
              padding: [2, 4]
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 30, 60, 0.95)',
          borderColor: 'rgba(0, 180, 255, 0.6)',
          borderWidth: 2,
          textStyle: {
            color: '#ffffff',
            fontSize: 14
          },
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <div style="font-size: 16px; font-weight: bold; color: #00e4ff; margin-bottom: 5px;">${params.name}</div>
              <div style="color: #b0e8ff;">营业额: ${formatCurrency(params.value[2])}</div>
            </div>`
          }
        }
      }

      mapChartInstance.setOption(option)

      // 地图设置完成后，触发趋势图表重新调整大小
      nextTick(() => {
        setTimeout(() => {
          if (revenueChartInstance) revenueChartInstance.resize()
          if (trafficChartInstance) trafficChartInstance.resize()
          if (rechargeChartInstance) rechargeChartInstance.resize()
        }, 50)
      })
    }
    
    // 不带地理数据的地图初始化（散点图fallback）
    const initMapWithoutGeo = () => {
      // 尝试获取真实坐标数据
      let storeData = []
      const mapCacheKey = `map_${currentRankingType.value}_${selectedTimeRange.value}`
      const cachedMapData = dataCache.value.mapData?.[mapCacheKey]

      if (cachedMapData && cachedMapData.stores && cachedMapData.stores.length > 0) {
        storeData = cachedMapData.stores
      } else {
        // 使用模拟数据作为fallback
        console.warn('No cached coordinate data for scatter plot, using mock data')
        storeData = mockData.getMapData().stores
      }

      // 计算坐标范围以自动调整视图
      const lngs = storeData.map(store => store.coord[0])
      const lats = storeData.map(store => store.coord[1])
      const minLng = Math.min(...lngs) - 0.1
      const maxLng = Math.max(...lngs) + 0.1
      const minLat = Math.min(...lats) - 0.1
      const maxLat = Math.max(...lats) + 0.1

      // 计算数值范围用于颜色映射
      const values = storeData.map(store => store.revenue || store.value || 0)
      const minValue = Math.min(...values)
      const maxValue = Math.max(...values)
      const legendData = generateColorLegend(minValue, maxValue, currentRankingType.value)

      const option = {
        backgroundColor: 'transparent',
        // 添加颜色图例
        graphic: [
          {
            type: 'group',
            left: 25,
            top: 25,
            children: [
              // 图例外层阴影
              {
                type: 'rect',
                shape: { x: 2, y: 2, width: 140, height: 90 },
                style: {
                  fill: 'rgba(0, 0, 0, 0.3)',
                  opacity: 0.6
                },
                z: 98
              },
              // 图例背景
              {
                type: 'rect',
                shape: { x: 0, y: 0, width: 140, height: 90 },
                style: {
                  fill: 'linear-gradient(135deg, rgba(0, 30, 60, 0.95), rgba(0, 50, 100, 0.9))',
                  stroke: 'rgba(0, 180, 255, 0.8)',
                  lineWidth: 2,
                  shadowBlur: 8,
                  shadowColor: 'rgba(0, 180, 255, 0.4)'
                },
                z: 99
              },
              // 图例标题背景
              {
                type: 'rect',
                shape: { x: 8, y: 8, width: 124, height: 22 },
                style: {
                  fill: 'rgba(0, 150, 255, 0.2)',
                  stroke: 'rgba(0, 180, 255, 0.5)',
                  lineWidth: 1
                },
                z: 100
              },
              // 图例标题
              {
                type: 'text',
                style: {
                  text: '数值范围',
                  x: 70,
                  y: 24,
                  textAlign: 'center',
                  fill: '#00e4ff',
                  fontSize: 13,
                  fontWeight: 'bold',
                  textShadow: '0 0 8px rgba(0, 228, 255, 0.6)'
                },
                z: 101
              },
              // 最大值颜色块
              {
                type: 'rect',
                shape: { x: 15, y: 38, width: 18, height: 16 },
                style: {
                  fill: legendData.maxColor,
                  stroke: 'rgba(255, 255, 255, 0.3)',
                  lineWidth: 1
                },
                z: 101
              },
              // 最大值标签
              {
                type: 'text',
                style: {
                  text: 'MAX:',
                  x: 38,
                  y: 48,
                  fill: '#b0e8ff',
                  fontSize: 11,
                  fontWeight: 'bold'
                },
                z: 101
              },
              // 最大值数值
              {
                type: 'text',
                style: {
                  text: legendData.maxLabel,
                  x: 65,
                  y: 48,
                  fill: '#ffffff',
                  fontSize: 10,
                  fontWeight: 'normal'
                },
                z: 101
              },
              // 最小值颜色块
              {
                type: 'rect',
                shape: { x: 15, y: 60, width: 18, height: 16 },
                style: {
                  fill: legendData.minColor,
                  stroke: 'rgba(255, 255, 255, 0.3)',
                  lineWidth: 1
                },
                z: 101
              },
              // 最小值标签
              {
                type: 'text',
                style: {
                  text: 'MIN:',
                  x: 38,
                  y: 70,
                  fill: '#b0e8ff',
                  fontSize: 11,
                  fontWeight: 'bold'
                },
                z: 101
              },
              // 最小值数值
              {
                type: 'text',
                style: {
                  text: legendData.minLabel,
                  x: 65,
                  y: 70,
                  fill: '#ffffff',
                  fontSize: 10,
                  fontWeight: 'normal'
                },
                z: 101
              }
            ]
          }
        ],
        grid: {
          left: '10%',
          right: '10%',
          top: '10%',
          bottom: '10%'
        },
        xAxis: {
          type: 'value',
          min: minLng,
          max: maxLng,
          show: false
        },
        yAxis: {
          type: 'value',
          min: minLat,
          max: maxLat,
          show: false
        },
        series: [{
          type: 'scatter',
          data: storeData.map(store => {
            const value = store.revenue || store.value || 0
            const color = getValueBasedColor(value, minValue, maxValue)
            return {
              name: store.name,
              value: [store.coord[0], store.coord[1], value],
              itemStyle: {
                color: color,
                shadowBlur: 12,
                shadowColor: color.replace('0.8)', '0.5)')
              }
            }
          }),
          symbolSize: function(val) {
            return Math.max(Math.sqrt(val[2] / 10000) + 18, 25)
          },
          itemStyle: {
            shadowBlur: 12
          },
          label: {
            show: true,
            position: 'top',
            distance: 8,
            color: '#ffffff',
            fontSize: 11,
            fontWeight: 'bold',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderColor: 'rgba(0, 150, 255, 0.8)',
            borderWidth: 1,
            borderRadius: 3,
            padding: [2, 4],
            formatter: function(params) {
              return params.name
            }
          }
        }],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 30, 60, 0.95)',
          borderColor: 'rgba(0, 180, 255, 0.6)',
          borderWidth: 2,
          textStyle: {
            color: '#ffffff',
            fontSize: 14
          },
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <div style="font-size: 16px; font-weight: bold; color: #00e4ff; margin-bottom: 5px;">${params.name}</div>
              <div style="color: #b0e8ff;">数值: ${formatCurrency(params.value[2])}</div>
              <div style="color: #a0d8ff; font-size: 12px;">坐标: ${params.value[0].toFixed(3)}, ${params.value[1].toFixed(3)}</div>
            </div>`
          }
        }
      }

      mapChartInstance.setOption(option)
      console.log(`Scatter plot initialized with ${storeData.length} stores`)
    }
    
    // 初始化趋势图表
    const initTrendCharts = async () => {
      const shopId = selectedStore.value === 'all' ? null : selectedStore.value
      const cacheKey = `trends_${shopId || 'all'}`

      // 检查缓存
      const cachedTrends = dataCache.value.trends?.[cacheKey]
      const now = Date.now()
      const cacheExpiry = 30000 // 30秒缓存

      let revenueData, trafficData, rechargeData
      let shouldUpdateCharts = false

      if (cachedTrends && (now - cachedTrends.timestamp) < cacheExpiry) {
        // 使用缓存数据
        revenueData = cachedTrends.revenueData
        trafficData = cachedTrends.trafficData
        rechargeData = cachedTrends.rechargeData
      } else {
        // 获取新数据
        try {
          [revenueData, trafficData, rechargeData] = await Promise.all([
            apiService.fetchTrendsData('revenue', shopId, 30),
            apiService.fetchTrendsData('traffic', shopId, 30),
            apiService.fetchTrendsData('recharge', shopId, 30)
          ])
          shouldUpdateCharts = true
        } catch (error) {
          console.error('获取趋势数据失败:', error)
          // 使用模拟数据
          revenueData = mockData.getTrends('revenue').values
          trafficData = mockData.getTrends('traffic').values
          rechargeData = mockData.getTrends('revenue').values
          shouldUpdateCharts = true
        }

        // 更新缓存
        if (!dataCache.value.trends) {
          dataCache.value.trends = {}
        }
        dataCache.value.trends[cacheKey] = {
          revenueData,
          trafficData,
          rechargeData,
          timestamp: now
        }
      }

      // 只有在数据变化或图表未初始化时才重新渲染
      if (shouldUpdateCharts || !revenueChartInstance || !trafficChartInstance || !rechargeChartInstance) {
      
      // 营业额趋势图
      if (revenueChart.value) {
        revenueChartInstance = echarts.init(revenueChart.value)
        const revenueOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: revenueData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(0, 180, 255, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b0e8ff',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(0, 180, 255, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b0e8ff',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: formatCurrency
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 180, 255, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: revenueData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 228, 255, 0.9)' },
                  { offset: 0.5, color: 'rgba(0, 228, 255, 0.5)' },
                  { offset: 1, color: 'rgba(0, 228, 255, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(0, 180, 255, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #00e4ff; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #b0e8ff;">营业额: ${formatCurrency(params[0].value)}</div>
              </div>`
            }
          }
        }
        revenueChartInstance.setOption(revenueOption)
      }
      
      // 客流趋势图
      if (trafficChart.value) {
        trafficChartInstance = echarts.init(trafficChart.value)
        const trafficOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: trafficData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(255, 107, 53, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#ffb399',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(255, 107, 53, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#ffb399',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: (value) => formatNumber(value) + '人'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 107, 53, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: trafficData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(255, 107, 53, 0.9)' },
                  { offset: 0.5, color: 'rgba(255, 107, 53, 0.5)' },
                  { offset: 1, color: 'rgba(255, 107, 53, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(255, 107, 53, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #ff6b35; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #ffb399;">客流量: ${formatNumber(params[0].value)}人</div>
              </div>`
            }
          }
        }
        trafficChartInstance.setOption(trafficOption)
      }
      
      // 充值金额趋势图
      if (rechargeChart.value) {
        rechargeChartInstance = echarts.init(rechargeChart.value)
        const rechargeOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: rechargeData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(102, 255, 102, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b3ffb3',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(102, 255, 102, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b3ffb3',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: formatCurrency
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(102, 255, 102, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: rechargeData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(102, 255, 102, 0.9)' },
                  { offset: 0.5, color: 'rgba(102, 255, 102, 0.5)' },
                  { offset: 1, color: 'rgba(102, 255, 102, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(102, 255, 102, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #66ff66; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #b3ffb3;">充值金额: ${formatCurrency(params[0].value)}</div>
              </div>`
            }
          }
        }
        rechargeChartInstance.setOption(rechargeOption)
      }
      } // 关闭条件渲染块
    }
    
    // 自动切换排行榜
    const startAutoSwitch = () => {
      const rankingTypes = ['revenue', 'traffic', 'staff', 'commission']
      let currentIndex = 0
      
      autoSwitchTimer = setInterval(() => {
        currentIndex = (currentIndex + 1) % rankingTypes.length
        currentRankingType.value = rankingTypes[currentIndex]
        updateRankings(currentRankingType.value)
      }, 5000)
    }
    
    const stopAutoSwitch = () => {
      if (autoSwitchTimer) {
        clearInterval(autoSwitchTimer)
        autoSwitchTimer = null
      }
    }
    
    // 事件处理
    const onStoreChange = () => {
      updateMetrics()
      nextTick(() => {
        initTrendCharts()
      })
    }
    
    const onTimeRangeChange = () => {
      updateMetrics()
      updateRankings(currentRankingType.value)
    }
    
    const switchRankingTab = (type) => {
      stopAutoSwitch()
      currentRankingType.value = type
      updateRankings(type)

      setTimeout(() => {
        startAutoSwitch()
      }, 10000)
    }

    // 调试函数：检查坐标数据状态
    const debugCoordinateData = () => {
      const mapCacheKey = `map_${currentRankingType.value}_${selectedTimeRange.value}`
      const cachedMapData = dataCache.value.mapData?.[mapCacheKey]

      console.log('=== Coordinate Data Debug ===')
      console.log('Current ranking type:', currentRankingType.value)
      console.log('Current time range:', selectedTimeRange.value)
      console.log('Map cache key:', mapCacheKey)
      console.log('Cached map data:', cachedMapData)
      console.log('Current rankings:', currentRankings.value)
      console.log('============================')
    }

    // 颜色计算函数：基于数值生成颜色
    const getValueBasedColor = (value, minValue, maxValue) => {
      if (maxValue === minValue) {
        return 'rgba(0, 228, 255, 0.8)' // 默认蓝色
      }

      // 计算归一化值 (0-1)
      const normalizedValue = (value - minValue) / (maxValue - minValue)

      // 使用HSL颜色空间：红色(0°) → 黄色(60°) → 绿色(120°)
      const hue = normalizedValue * 120 // 0-120度
      const saturation = 80 // 80%饱和度
      const lightness = 50 // 50%亮度

      return `hsla(${hue}, ${saturation}%, ${lightness}%, 0.8)`
    }

    // 计算最优缩放级别
    const calculateOptimalZoom = (stores) => {
      if (!stores || stores.length === 0) return 1.5

      const coords = stores.map(store => store.coord)
      const lngs = coords.map(coord => coord[0])
      const lats = coords.map(coord => coord[1])

      const minLng = Math.min(...lngs)
      const maxLng = Math.max(...lngs)
      const minLat = Math.min(...lats)
      const maxLat = Math.max(...lats)

      // 计算坐标跨度
      const lngSpan = maxLng - minLng
      const latSpan = maxLat - minLat
      const maxSpan = Math.max(lngSpan, latSpan)

      // 根据坐标分布密度调整缩放
      if (maxSpan < 0.1) return 3.0      // 非常集中
      if (maxSpan < 0.2) return 2.5      // 比较集中
      if (maxSpan < 0.5) return 2.0      // 中等分散
      return 1.5                         // 分散
    }

    // 生成颜色图例数据
    const generateColorLegend = (minValue, maxValue, type) => {
      const formatValue = (value) => {
        if (type === 'revenue' || type === 'commission') {
          return formatCurrency(value)
        } else if (type === 'traffic' || type === 'staff') {
          return formatNumber(value) + '人'
        }
        return value.toString()
      }

      return {
        minValue,
        maxValue,
        minColor: getValueBasedColor(minValue, minValue, maxValue),
        maxColor: getValueBasedColor(maxValue, minValue, maxValue),
        minLabel: formatValue(minValue),
        maxLabel: formatValue(maxValue)
      }
    }
    
    // 窗口大小变化处理
    const handleResize = () => {
      windowWidth.value = window.innerWidth
      if (mapChartInstance && !isMobile.value) mapChartInstance.resize()
      if (revenueChartInstance) revenueChartInstance.resize()
      if (trafficChartInstance) trafficChartInstance.resize()
      if (rechargeChartInstance) rechargeChartInstance.resize()
    }
    
    // 生命周期
    onMounted(async () => {
      // 更新日期时间
      updateDateTime()
      dateTimeTimer = setInterval(updateDateTime, 1000)
      
      // 加载门店数据
      await loadStores()
      
      // 等待DOM渲染完成
      await nextTick()
      
      // 初始化图表
      await initTrendCharts()

      if (!isMobile.value) {
        await initMapChart()
        // 地图初始化后再次调整趋势图表
        setTimeout(() => {
          if (revenueChartInstance) revenueChartInstance.resize()
          if (trafficChartInstance) trafficChartInstance.resize()
          if (rechargeChartInstance) rechargeChartInstance.resize()
        }, 200)
      }
      
      // 初始化数据
      updateMetrics()
      updateRankings()
      
      // 启动自动切换
      startAutoSwitch()
      
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
    })
    
    onUnmounted(() => {
      if (dateTimeTimer) clearInterval(dateTimeTimer)
      if (autoSwitchTimer) clearInterval(autoSwitchTimer)
      window.removeEventListener('resize', handleResize)
      
      // 销毁图表实例
      if (mapChartInstance) mapChartInstance.dispose()
      if (revenueChartInstance) revenueChartInstance.dispose()
      if (trafficChartInstance) trafficChartInstance.dispose()
      if (rechargeChartInstance) rechargeChartInstance.dispose()
    })
    
    return {
      // 响应式数据
      currentDateTime,
      selectedStore,
      selectedTimeRange,
      currentRankingType,
      stores,
      rankingTabs,
      metrics,
      currentRankings,

      // 计算属性
      isMobile,

      // 模板引用
      mapChart,
      revenueChart,
      trafficChart,
      rechargeChart,

      // 方法
      formatRankingValue,
      getRankClass,
      onStoreChange,
      onTimeRangeChange,
      switchRankingTab,
      debugCoordinateData,
      getValueBasedColor,
      calculateOptimalZoom,
      generateColorLegend
    }
  }
}
</script>