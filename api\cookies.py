# Cookie管理API

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from config import get_logger
import os
import re

logger = get_logger(__name__)

router = APIRouter(prefix="/api/cookies", tags=["Cookie管理"])

class CookieItem(BaseModel):
    """Cookie项模型"""
    name: str
    value: str
    domain: Optional[str] = None
    path: Optional[str] = None

class CookieResponse(BaseModel):
    """Cookie响应模型"""
    type: str  # 'default' or 'mt' (美团)
    cookies: List[CookieItem]
    raw_cookie_string: str

class UpdateCookieRequest(BaseModel):
    """更新Cookie请求模型"""
    type: str  # 'default' or 'mt'
    cookie_string: str

def parse_cookie_string(cookie_string: str) -> List[CookieItem]:
    """解析Cookie字符串为Cookie项列表"""
    cookies = []
    if not cookie_string:
        return cookies
    
    # 分割Cookie字符串
    cookie_pairs = cookie_string.split(';')
    
    for pair in cookie_pairs:
        pair = pair.strip()
        if '=' in pair:
            name, value = pair.split('=', 1)
            cookies.append(CookieItem(
                name=name.strip(),
                value=value.strip()
            ))
    
    return cookies

def get_cookie_from_file() -> Dict[str, str]:
    """从crawler/index.py文件中读取Cookie"""
    try:
        crawler_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "crawler", "index.py")
        
        if not os.path.exists(crawler_file):
            logger.error(f"Crawler文件不存在: {crawler_file}")
            return {"cookie": "", "mt_cookie": ""}
        
        with open(crawler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取cookie和mt_cookie
        cookie_match = re.search(r'cookie\s*=\s*["\']([^"\']*)["\']', content)
        mt_cookie_match = re.search(r'mt_cookie\s*=\s*["\']([^"\']*)["\']', content)
        
        cookie = cookie_match.group(1) if cookie_match else ""
        mt_cookie = mt_cookie_match.group(1) if mt_cookie_match else ""
        
        return {"cookie": cookie, "mt_cookie": mt_cookie}
        
    except Exception as e:
        logger.error(f"读取Cookie文件失败: {str(e)}")
        return {"cookie": "", "mt_cookie": ""}

def update_cookie_in_file(cookie_type: str, new_cookie: str) -> bool:
    """更新crawler/index.py文件中的Cookie"""
    try:
        crawler_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "crawler", "index.py")
        
        if not os.path.exists(crawler_file):
            logger.error(f"Crawler文件不存在: {crawler_file}")
            return False
        
        with open(crawler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 根据类型更新对应的cookie
        if cookie_type == "default":
            # 更新cookie变量
            content = re.sub(
                r'cookie\s*=\s*["\'][^"\']*["\']',
                f'cookie = "{new_cookie}"',
                content
            )
        elif cookie_type == "mt":
            # 更新mt_cookie变量
            content = re.sub(
                r'mt_cookie\s*=\s*["\'][^"\']*["\']',
                f'mt_cookie = "{new_cookie}"',
                content
            )
        else:
            logger.error(f"不支持的Cookie类型: {cookie_type}")
            return False
        
        # 写回文件
        with open(crawler_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Cookie更新成功: {cookie_type}")
        return True
        
    except Exception as e:
        logger.error(f"更新Cookie文件失败: {str(e)}")
        return False

@router.get("/", response_model=List[CookieResponse], summary="获取所有Cookie")
async def get_all_cookies():
    """获取所有Cookie配置"""
    try:
        cookies_data = get_cookie_from_file()
        
        responses = []
        
        # 默认Cookie
        default_cookies = parse_cookie_string(cookies_data["cookie"])
        responses.append(CookieResponse(
            type="default",
            cookies=default_cookies,
            raw_cookie_string=cookies_data["cookie"]
        ))
        
        # 美团Cookie
        mt_cookies = parse_cookie_string(cookies_data["mt_cookie"])
        responses.append(CookieResponse(
            type="mt",
            cookies=mt_cookies,
            raw_cookie_string=cookies_data["mt_cookie"]
        ))
        
        return responses
        
    except Exception as e:
        logger.error(f"获取Cookie失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Cookie失败: {str(e)}")

@router.get("/{cookie_type}", response_model=CookieResponse, summary="获取指定类型Cookie")
async def get_cookie_by_type(cookie_type: str):
    """获取指定类型的Cookie"""
    try:
        if cookie_type not in ["default", "mt"]:
            raise HTTPException(status_code=400, detail="Cookie类型必须是 'default' 或 'mt'")
        
        cookies_data = get_cookie_from_file()
        
        if cookie_type == "default":
            cookie_string = cookies_data["cookie"]
        else:
            cookie_string = cookies_data["mt_cookie"]
        
        cookies = parse_cookie_string(cookie_string)
        
        return CookieResponse(
            type=cookie_type,
            cookies=cookies,
            raw_cookie_string=cookie_string
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Cookie失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Cookie失败: {str(e)}")

@router.put("/{cookie_type}", summary="更新指定类型Cookie")
async def update_cookie(cookie_type: str, request: UpdateCookieRequest):
    """更新指定类型的Cookie"""
    try:
        if cookie_type not in ["default", "mt"]:
            raise HTTPException(status_code=400, detail="Cookie类型必须是 'default' 或 'mt'")
        
        if request.type != cookie_type:
            raise HTTPException(status_code=400, detail="请求中的Cookie类型与URL参数不匹配")
        
        # 验证Cookie格式
        try:
            parse_cookie_string(request.cookie_string)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Cookie格式无效: {str(e)}")
        
        # 更新Cookie
        success = update_cookie_in_file(cookie_type, request.cookie_string)
        
        if not success:
            raise HTTPException(status_code=500, detail="更新Cookie失败")
        
        return {
            "status": "success",
            "message": f"Cookie类型 '{cookie_type}' 更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新Cookie失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新Cookie失败: {str(e)}")

@router.delete("/{cookie_type}", summary="清空指定类型Cookie")
async def clear_cookie(cookie_type: str):
    """清空指定类型的Cookie"""
    try:
        if cookie_type not in ["default", "mt"]:
            raise HTTPException(status_code=400, detail="Cookie类型必须是 'default' 或 'mt'")
        
        # 清空Cookie（设置为空字符串）
        success = update_cookie_in_file(cookie_type, "")
        
        if not success:
            raise HTTPException(status_code=500, detail="清空Cookie失败")
        
        return {
            "status": "success",
            "message": f"Cookie类型 '{cookie_type}' 已清空"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空Cookie失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空Cookie失败: {str(e)}")
