# Git相关
.git
.gitignore

# Python相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# 虚拟环境
venv/
env/
ENV/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 前端相关
frontend/node_modules/
frontend/dist/
frontend/.nuxt/
frontend/.next/
frontend/out/
frontend/build/

# 文档和说明
README.md
*.md

# 测试文件
tests/
test_*

# 临时文件
*.tmp
*.temp

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（可能包含敏感信息）
.env
.env.local
.env.production

# 构建产物
html/
dist/
build/