"""
数据模型定义
"""
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class SyncDataRequest(BaseModel):
    date: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "date": "2025-01-26"
            }
        }

class SyncDataResponse(BaseModel):
    success: bool
    message: str
    date: str

class OrderInfo(BaseModel):
    order_id: int
    order_display_id: str
    shop_id: int
    status: int
    amount: float
    payed_amount: Optional[float] = None
    pay_amount: Optional[float] = None
    customer_name: Optional[str] = None
    mobile_no: Optional[str] = None
    membership_name: Optional[str] = None
    membership_no: Optional[int] = None
    create_order_time: datetime
    pay_time: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class OrderQueryResponse(BaseModel):
    success: bool
    message: str
    date: str
    shop_id: Optional[int] = None
    total_count: int
    orders: List[OrderInfo]