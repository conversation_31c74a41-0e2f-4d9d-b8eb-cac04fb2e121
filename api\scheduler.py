# 定时任务管理API

import asyncio
from concurrent.futures import ThreadPoolExecutor
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from scheduler.sync_scheduler import data_sync_scheduler
from config import get_logger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/scheduler", tags=["定时任务管理"])

# 创建线程池执行器
thread_pool = ThreadPoolExecutor(max_workers=2)

class JobStatusResponse(BaseModel):
    """任务状态响应模型"""
    id: str
    name: str
    next_run_time: Optional[str]
    trigger: str

class IntervalUpdateRequest(BaseModel):
    """间隔更新请求模型"""
    minutes: int

class CronUpdateRequest(BaseModel):
    """Cron表达式更新请求模型"""
    hour: int
    minute: int
    second: int = 0

class TaskCreateRequest(BaseModel):
    """任务创建请求模型"""
    name: str
    task_type: str  # 'sync_current', 'sync_yesterday', 'test'
    trigger_type: str  # 'interval' or 'cron'
    interval_minutes: Optional[int] = None
    cron_hour: Optional[int] = None
    cron_minute: Optional[int] = None
    cron_second: int = 0

@router.get("/status", summary="获取调度器状态")
async def get_scheduler_status():
    """获取调度器运行状态"""
    try:
        return {
            "status": "success",
            "data": {
                "is_running": data_sync_scheduler.is_running,
                "message": "调度器运行中" if data_sync_scheduler.is_running else "调度器未启动"
            }
        }
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取调度器状态失败: {str(e)}")

@router.post("/start", summary="启动调度器")
async def start_scheduler():
    """启动定时任务调度器"""
    try:
        data_sync_scheduler.start()
        return {
            "status": "success",
            "message": "定时任务调度器启动成功"
        }
    except Exception as e:
        logger.error(f"启动调度器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动调度器失败: {str(e)}")

@router.post("/stop", summary="停止调度器")
async def stop_scheduler():
    """停止定时任务调度器"""
    try:
        data_sync_scheduler.stop()
        return {
            "status": "success",
            "message": "定时任务调度器停止成功"
        }
    except Exception as e:
        logger.error(f"停止调度器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止调度器失败: {str(e)}")

@router.get("/jobs", response_model=List[JobStatusResponse], summary="获取所有任务")
async def get_all_jobs():
    """获取所有定时任务"""
    try:
        jobs = data_sync_scheduler.get_jobs()
        job_list = []
        for job in jobs:
            job_list.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return job_list
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.get("/jobs/{job_id}", response_model=JobStatusResponse, summary="获取指定任务状态")
async def get_job_status(job_id: str):
    """获取指定任务的状态"""
    try:
        job = data_sync_scheduler.get_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")

        return {
            "id": job.id,
            "name": job.name,
            "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
            "trigger": str(job.trigger)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.post("/jobs/{job_id}/pause", summary="暂停任务")
async def pause_job(job_id: str):
    """暂停指定的定时任务"""
    try:
        data_sync_scheduler.pause_job(job_id)
        return {
            "status": "success",
            "message": f"任务 {job_id} 暂停成功"
        }
    except Exception as e:
        logger.error(f"暂停任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停任务失败: {str(e)}")

@router.post("/jobs/{job_id}/resume", summary="恢复任务")
async def resume_job(job_id: str):
    """恢复指定的定时任务"""
    try:
        data_sync_scheduler.resume_job(job_id)
        return {
            "status": "success",
            "message": f"任务 {job_id} 恢复成功"
        }
    except Exception as e:
        logger.error(f"恢复任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复任务失败: {str(e)}")

@router.put("/jobs/{job_id}/interval", summary="修改任务执行间隔")
async def update_job_interval(job_id: str, request: IntervalUpdateRequest):
    """修改任务的执行间隔"""
    try:
        if request.minutes < 1:
            raise HTTPException(status_code=400, detail="执行间隔不能小于1分钟")
        
        data_sync_scheduler.modify_job_interval(job_id, request.minutes)
        return {
            "status": "success",
            "message": f"任务 {job_id} 执行间隔已修改为 {request.minutes} 分钟"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改任务间隔失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"修改任务间隔失败: {str(e)}")

@router.post("/sync/now", summary="立即执行数据同步")
async def sync_data_now():
    """立即执行一次数据同步"""
    try:
        # 在线程池中执行当前数据同步
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(thread_pool, data_sync_scheduler.sync_current_data)
        return {
            "status": "success",
            "message": "数据同步执行成功"
        }
    except Exception as e:
        logger.error(f"立即同步数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"立即同步数据失败: {str(e)}")

@router.post("/sync/yesterday", summary="立即同步昨天数据")
async def sync_yesterday_now():
    """立即执行昨天数据同步"""
    try:
        # 在线程池中执行昨天数据同步
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(thread_pool, data_sync_scheduler.sync_yesterday_data)
        return {
            "status": "success",
            "message": "昨天数据同步执行成功"
        }
    except Exception as e:
        logger.error(f"立即同步昨天数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"立即同步昨天数据失败: {str(e)}")

@router.delete("/jobs/{job_id}", summary="删除任务")
async def delete_job(job_id: str):
    """删除指定的定时任务"""
    try:
        # 检查任务是否存在
        job = data_sync_scheduler.get_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")

        # 删除任务
        data_sync_scheduler.scheduler.remove_job(job_id)
        return {
            "status": "success",
            "message": f"任务 {job_id} 删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")

@router.put("/jobs/{job_id}/cron", summary="修改任务为Cron触发器")
async def update_job_cron(job_id: str, request: CronUpdateRequest):
    """修改任务为Cron触发器"""
    try:
        # 检查任务是否存在
        job = data_sync_scheduler.get_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")

        # 验证时间参数
        if not (0 <= request.hour <= 23):
            raise HTTPException(status_code=400, detail="小时必须在0-23之间")
        if not (0 <= request.minute <= 59):
            raise HTTPException(status_code=400, detail="分钟必须在0-59之间")
        if not (0 <= request.second <= 59):
            raise HTTPException(status_code=400, detail="秒必须在0-59之间")

        # 修改任务触发器
        data_sync_scheduler.scheduler.modify_job(
            job_id,
            trigger=CronTrigger(
                hour=request.hour,
                minute=request.minute,
                second=request.second
            )
        )

        return {
            "status": "success",
            "message": f"任务 {job_id} 已修改为Cron触发器: {request.hour:02d}:{request.minute:02d}:{request.second:02d}"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改任务Cron触发器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"修改任务Cron触发器失败: {str(e)}")

@router.post("/jobs", summary="创建新任务")
async def create_job(request: TaskCreateRequest):
    """创建新的定时任务"""
    try:
        # 验证任务类型
        task_functions = {
            'sync_current': data_sync_scheduler.sync_current_data,
            'sync_yesterday': data_sync_scheduler.sync_yesterday_data,
            'test': data_sync_scheduler.test_task
        }

        if request.task_type not in task_functions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的任务类型: {request.task_type}。支持的类型: {list(task_functions.keys())}"
            )

        # 生成任务ID
        job_id = f"{request.task_type}_{request.name.replace(' ', '_')}"

        # 根据触发器类型创建触发器
        if request.trigger_type == "interval":
            if not request.interval_minutes or request.interval_minutes < 1:
                raise HTTPException(status_code=400, detail="间隔触发器需要有效的间隔分钟数（>=1）")
            trigger = IntervalTrigger(minutes=request.interval_minutes)
        elif request.trigger_type == "cron":
            if request.cron_hour is None or request.cron_minute is None:
                raise HTTPException(status_code=400, detail="Cron触发器需要指定小时和分钟")
            if not (0 <= request.cron_hour <= 23):
                raise HTTPException(status_code=400, detail="小时必须在0-23之间")
            if not (0 <= request.cron_minute <= 59):
                raise HTTPException(status_code=400, detail="分钟必须在0-59之间")
            trigger = CronTrigger(
                hour=request.cron_hour,
                minute=request.cron_minute,
                second=request.cron_second
            )
        else:
            raise HTTPException(status_code=400, detail="触发器类型必须是 'interval' 或 'cron'")

        # 添加任务
        data_sync_scheduler.scheduler.add_job(
            func=task_functions[request.task_type],
            trigger=trigger,
            id=job_id,
            name=request.name,
            replace_existing=True
        )

        return {
            "status": "success",
            "message": f"任务 '{request.name}' 创建成功",
            "job_id": job_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")