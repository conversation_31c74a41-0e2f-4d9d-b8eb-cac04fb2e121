"""
提成数据访问对象
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, desc, func
from datetime import datetime

from ..connection import DatabaseManager
from .model import Commission
from config import get_logger

logger = get_logger(__name__)


class CommissionDAO:
    """提成数据访问对象"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def batch_insert_or_update(self, commissions_data: List[Dict[str, Any]], order_id: str) -> Dict[str, int]:
        """批量插入或更新提成数据"""
        with self.db_manager.get_session_context() as session:
            result = {'inserted': 0, 'updated': 0, 'errors': 0}
            
            try:
                for commission_data in commissions_data:
                    try:
                        employee_id = commission_data.get('employeeId')
                        employ_type = commission_data.get('employ_type')
                        
                        if not employee_id or not employ_type:
                            result['errors'] += 1
                            continue
                        
                        # 查找是否存在相同的记录（同一订单项、同一订单、同一员工、同一类型）
                        order_item_id = commission_data.get('orderItemId')
                        if not order_item_id:
                            result['errors'] += 1
                            continue
                            
                        existing = session.query(Commission).filter(
                            and_(
                                Commission.order_item_id == int(order_item_id),
                                Commission.order_id == int(order_id),
                                Commission.employee_id == employee_id,
                                Commission.employ_type == employ_type
                            )
                        ).first()
                        
                        if existing:
                            # 更新现有记录
                            self._update_commission_from_data(existing, commission_data)
                            result['updated'] += 1
                        else:
                            # 创建新记录
                            commission = Commission.from_commission_data(commission_data, order_id, order_item_id)
                            session.add(commission)
                            result['inserted'] += 1
                            
                    except Exception as e:
                        logger.error(f"处理员工 {commission_data.get('employeeId', 'unknown')} 提成时出错: {e}")
                        result['errors'] += 1
                        continue
                
                logger.info(f"提成数据批量处理完成: 插入 {result['inserted']} 条，更新 {result['updated']} 条，错误 {result['errors']} 条")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"批量处理提成数据失败: {e}")
                result['errors'] = len(commissions_data)
                return result
    
    def _update_commission_from_data(self, commission: Commission, data: Dict[str, Any]):
        """从数据更新提成信息"""
        commission.employee_name = data.get('employeeName')
        commission.commission = data.get('commission', 0.0)
        commission.shop_id = data.get('shop_id')
        commission.date = data.get('date')
        commission.updated_at = datetime.now()
    
    def get_commissions_by_order(self, order_id: int) -> List[Commission]:
        """根据订单ID获取提成列表"""
        with self.db_manager.get_session_context() as session:
            try:
                commissions = session.query(Commission).filter(
                    Commission.order_id == order_id
                ).order_by(Commission.employee_id).all()
                
                # 确保所有属性都已加载并分离对象
                for commission in commissions:
                    for column in Commission.__table__.columns:
                        getattr(commission, column.name)
                
                session.expunge_all()
                return commissions
                
            except SQLAlchemyError as e:
                logger.error(f"查询订单提成信息失败: {e}")
                return []

    def get_commissions_by_employee(self, employee_id: int, date: str, shop_id: Optional[int] = None) -> List[Commission]:
        """根据员工ID和日期获取提成列表"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Commission).filter(
                    and_(
                        Commission.employee_id == employee_id,
                        Commission.date == date
                    )
                )
                
                if shop_id:
                    query = query.filter(Commission.shop_id == shop_id)
                
                commissions = query.order_by(Commission.order_id).all()
                
                # 确保所有属性都已加载并分离对象
                for commission in commissions:
                    for column in Commission.__table__.columns:
                        getattr(commission, column.name)
                
                session.expunge_all()
                return commissions
                
            except SQLAlchemyError as e:
                logger.error(f"查询员工提成信息失败: {e}")
                return []

    def get_commissions_by_shop_date(self, shop_id: int, date: str) -> List[Commission]:
        """根据店铺ID和日期获取提成列表"""
        with self.db_manager.get_session_context() as session:
            try:
                commissions = session.query(Commission).filter(
                    and_(
                        Commission.shop_id == shop_id,
                        Commission.date == date
                    )
                ).order_by(Commission.employee_id, Commission.order_id).all()
                
                # 确保所有属性都已加载并分离对象
                for commission in commissions:
                    for column in Commission.__table__.columns:
                        getattr(commission, column.name)
                
                session.expunge_all()
                return commissions
                
            except SQLAlchemyError as e:
                logger.error(f"查询店铺提成信息失败: {e}")
                return []

    def get_commissions_by_date_range(self, start_date: str, end_date: str, shop_id: Optional[int] = None, employee_id: Optional[int] = None) -> List[Commission]:
        """根据日期范围获取提成数据"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Commission).filter(
                    and_(
                        Commission.date >= start_date,
                        Commission.date <= end_date
                    )
                )
                
                if shop_id:
                    query = query.filter(Commission.shop_id == shop_id)
                
                if employee_id:
                    query = query.filter(Commission.employee_id == employee_id)
                
                commissions = query.order_by(Commission.date.desc(), Commission.employee_id, Commission.order_id).all()
                
                # 确保所有属性都已加载并分离对象
                for commission in commissions:
                    for column in Commission.__table__.columns:
                        getattr(commission, column.name)
                
                session.expunge_all()
                return commissions
                
            except SQLAlchemyError as e:
                logger.error(f"查询日期范围提成数据失败: {e}")
                return []

    def get_commission_statistics(self, shop_id: int, date: str) -> Dict[str, Any]:
        """获取提成统计信息"""
        with self.db_manager.get_session_context() as session:
            try:
                # 总提成金额
                total_commission = session.query(func.sum(Commission.commission)).filter(
                    and_(
                        Commission.shop_id == shop_id,
                        Commission.date == date
                    )
                ).scalar() or 0.0
                
                # 销售提成金额
                sales_commission = session.query(func.sum(Commission.commission)).filter(
                    and_(
                        Commission.shop_id == shop_id,
                        Commission.date == date,
                        Commission.employ_type == 'sale'
                    )
                ).scalar() or 0.0
                
                # 服务提成金额
                service_commission = session.query(func.sum(Commission.commission)).filter(
                    and_(
                        Commission.shop_id == shop_id,
                        Commission.date == date,
                        Commission.employ_type == 'service'
                    )
                ).scalar() or 0.0
                
                # 提成人数
                commission_count = session.query(func.count(func.distinct(Commission.employee_id))).filter(
                    and_(
                        Commission.shop_id == shop_id,
                        Commission.date == date
                    )
                ).scalar() or 0
                
                return {
                    'total_commission': float(total_commission),
                    'sales_commission': float(sales_commission),
                    'service_commission': float(service_commission),
                    'commission_employee_count': commission_count
                }
                
            except SQLAlchemyError as e:
                logger.error(f"获取提成统计信息失败: {e}")
                return {
                    'total_commission': 0.0,
                    'sales_commission': 0.0,
                    'service_commission': 0.0,
                    'commission_employee_count': 0
                }

    def delete_commissions_by_order(self, order_id: int) -> bool:
        """根据订单ID删除提成数据"""
        with self.db_manager.get_session_context() as session:
            try:
                deleted_count = session.query(Commission).filter(
                    Commission.order_id == order_id
                ).delete()
                
                logger.info(f"删除订单 {order_id} 的 {deleted_count} 条提成记录")
                return True
                
            except SQLAlchemyError as e:
                logger.error(f"删除订单提成数据失败: {e}")
                return False


# 创建DAO实例
commission_dao = CommissionDAO()