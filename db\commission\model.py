"""
提成数据模型
"""
from sqlalchemy import Column, Integer, String, Float, BigInteger, Index, DateTime
from datetime import datetime
from typing import Dict, Any, Optional
from db.connection import Base


class Commission(Base):
    """员工提成数据表"""
    __tablename__ = 'commissions'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    
    # 订单信息
    order_id = Column(BigInteger, nullable=False, comment='订单ID')
    order_item_id = Column(BigInteger, nullable=False, comment='订单项ID')
    
    # 员工信息
    employee_id = Column(BigInteger, nullable=False, comment='员工ID')
    employee_name = Column(String(100), nullable=False, comment='员工姓名')
    
    # 提成信息
    commission = Column(Float, nullable=False, default=0.0, comment='提成金额')
    employ_type = Column(String(20), nullable=False, comment='员工类型：sale(销售), service(服务)')
    
    # 店铺信息
    shop_id = Column(BigInteger, nullable=False, comment='店铺ID')
    
    # 时间信息
    date = Column(String(10), nullable=False, comment='数据日期，格式：YYYY-MM-DD')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 索引
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_employee_id', 'employee_id'),
        Index('idx_shop_id', 'shop_id'),
        Index('idx_date', 'date'),
        Index('idx_employ_type', 'employ_type'),
        Index('idx_employee_shop_date', 'employee_id', 'shop_id', 'date'),
        Index('idx_order_employee', 'order_id', 'employee_id'),
        Index('idx_order_item_employee', 'order_item_id', 'employee_id'),
        Index('idx_order_item_employee_type', 'order_item_id', 'employee_id', 'employ_type'),
    )
    
    @classmethod
    def from_commission_data(cls, commission_data: Dict[str, Any], order_id: str, order_item_id: str) -> 'Commission':
        """从提成数据创建Commission实例"""
        return cls(
            order_id=int(order_id),
            order_item_id=int(order_item_id),
            employee_id=commission_data.get('employeeId'),
            employee_name=commission_data.get('employeeName'),
            commission=commission_data.get('commission', 0.0),
            employ_type=commission_data.get('employ_type'),
            shop_id=commission_data.get('shop_id'),
            date=commission_data.get('date')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'order_item_id': self.order_item_id,
            'employee_id': self.employee_id,
            'employee_name': self.employee_name,
            'commission': self.commission,
            'employ_type': self.employ_type,
            'shop_id': self.shop_id,
            'date': self.date,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<Commission(id={self.id}, order_id={self.order_id}, employee_id={self.employee_id}, employee_name='{self.employee_name}', commission={self.commission}, employ_type='{self.employ_type}')>"