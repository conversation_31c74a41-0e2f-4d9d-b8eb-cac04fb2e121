"""
员工数据访问对象
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, desc, func
from datetime import datetime

from ..connection import DatabaseManager
from .model import Employee
from config import get_logger

logger = get_logger(__name__)


class EmployeeDAO:
    """员工数据访问对象"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def batch_insert_or_update(self, employees_data: List[Dict[str, Any]], date: str) -> Dict[str, int]:
        """批量插入或更新员工数据"""
        with self.db_manager.get_session_context() as session:
            result = {'inserted': 0, 'updated': 0, 'errors': 0}
            
            try:
                for employee_data in employees_data:
                    try:
                        employee_id = employee_data.get('employeeId')
                        shop_id = employee_data.get('shopId')
                        
                        if not employee_id or not shop_id:
                            result['errors'] += 1
                            continue
                        
                        # 查找是否存在相同的记录（同一员工、同一店铺、同一日期）
                        existing = session.query(Employee).filter(
                            and_(
                                Employee.employee_id == employee_id,
                                Employee.shop_id == shop_id,
                                Employee.date == date
                            )
                        ).first()
                        
                        if existing:
                            # 更新现有记录
                            self._update_employee_from_data(existing, employee_data)
                            result['updated'] += 1
                        else:
                            # 创建新记录
                            employee = Employee.from_api_data(employee_data, date)
                            session.add(employee)
                            result['inserted'] += 1
                            
                    except Exception as e:
                        logger.error(f"处理员工 {employee_data.get('employeeId', 'unknown')} 时出错: {e}")
                        result['errors'] += 1
                        continue
                
                logger.info(f"员工数据批量处理完成: 插入 {result['inserted']} 条，更新 {result['updated']} 条，错误 {result['errors']} 条")
                return result
                
            except SQLAlchemyError as e:
                logger.error(f"批量处理员工数据失败: {e}")
                result['errors'] = len(employees_data)
                return result
    
    def _update_employee_from_data(self, employee: Employee, data: Dict[str, Any]):
        """从API数据更新员工信息"""
        employee.employee_code = data.get('employeeCode')
        employee.employee_name = data.get('employeeName')
        employee.shop_name = data.get('shopName')
        employee.additional_clock_num = data.get('additionalClockNum', 0.0)
        employee.call_clock_num = data.get('callClockNum', 0.0)
        employee.extend_additional_clock_num = data.get('extendAdditionalClockNum', 0.0)
        employee.extend_call_clock_num = data.get('extendCallClockNum', 0.0)
        employee.extend_in_turn_clock_num = data.get('extendInTurnClockNum', 0.0)
        employee.extend_pointed_clock_num = data.get('extendPointedClockNum', 0.0)
        employee.in_turn_clock_num = data.get('inTurnClockNum', 0.0)
        employee.main_clock_num = data.get('mainClockNum', 0)
        employee.pointed_clock_num = data.get('pointedClockNum', 0.0)
        employee.small_clock_num = data.get('smallClockNum', 0)
        employee.total_clock_num = data.get('totalClockNum', 0.0)
        employee.total_item_num = data.get('totalItemNum', 0)
        employee.updated_at = datetime.now()
    
    def get_employee_by_id(self, employee_id: int, shop_id: int, date: str) -> Optional[Employee]:
        """根据员工ID、店铺ID和日期获取员工信息"""
        with self.db_manager.get_session_context() as session:
            try:
                employee = session.query(Employee).filter(
                    and_(
                        Employee.employee_id == employee_id,
                        Employee.shop_id == shop_id,
                        Employee.date == date
                    )
                ).first()
                
                if employee:
                    # 确保所有属性都已加载
                    _ = employee.id
                    _ = employee.employee_id
                    _ = employee.employee_code
                    _ = employee.employee_name
                    _ = employee.shop_id
                    _ = employee.shop_name
                    _ = employee.additional_clock_num
                    _ = employee.call_clock_num
                    _ = employee.extend_additional_clock_num
                    _ = employee.extend_call_clock_num
                    _ = employee.extend_in_turn_clock_num
                    _ = employee.extend_pointed_clock_num
                    _ = employee.in_turn_clock_num
                    _ = employee.main_clock_num
                    _ = employee.pointed_clock_num
                    _ = employee.small_clock_num
                    _ = employee.total_clock_num
                    _ = employee.total_item_num
                    _ = employee.date
                    _ = employee.created_at
                    _ = employee.updated_at
                    
                    # 将对象从Session中分离
                    session.expunge(employee)
                
                return employee
            except SQLAlchemyError as e:
                logger.error(f"查询员工信息失败: {e}")
                return None

    def get_employees_by_shop(self, shop_id: int, date: str) -> List[Employee]:
        """根据店铺ID和日期获取员工列表"""
        with self.db_manager.get_session_context() as session:
            try:
                employees = session.query(Employee).filter(
                    and_(
                        Employee.shop_id == shop_id,
                        Employee.date == date
                    )
                ).order_by(Employee.employee_id).all()
                
                # 确保所有属性都已加载
                for employee in employees:
                    _ = employee.id
                    _ = employee.employee_id
                    _ = employee.employee_code
                    _ = employee.employee_name
                    _ = employee.shop_id
                    _ = employee.shop_name
                    _ = employee.additional_clock_num
                    _ = employee.call_clock_num
                    _ = employee.extend_additional_clock_num
                    _ = employee.extend_call_clock_num
                    _ = employee.extend_in_turn_clock_num
                    _ = employee.extend_pointed_clock_num
                    _ = employee.in_turn_clock_num
                    _ = employee.main_clock_num
                    _ = employee.pointed_clock_num
                    _ = employee.small_clock_num
                    _ = employee.total_clock_num
                    _ = employee.total_item_num
                    _ = employee.date
                    _ = employee.created_at
                    _ = employee.updated_at
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return employees
            except SQLAlchemyError as e:
                logger.error(f"查询店铺员工列表失败: {e}")
                return []

    def get_employees_by_date_range(self, start_date: str, end_date: str, shop_id: Optional[int] = None) -> List[Employee]:
        """根据日期范围获取员工数据"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Employee).filter(
                    and_(
                        Employee.date >= start_date,
                        Employee.date <= end_date
                    )
                )
                
                if shop_id:
                    query = query.filter(Employee.shop_id == shop_id)
                
                employees = query.order_by(Employee.date.desc(), Employee.employee_id).all()
                
                # 确保所有属性都已加载
                for employee in employees:
                    _ = employee.id
                    _ = employee.employee_id
                    _ = employee.employee_code
                    _ = employee.employee_name
                    _ = employee.shop_id
                    _ = employee.shop_name
                    _ = employee.additional_clock_num
                    _ = employee.call_clock_num
                    _ = employee.extend_additional_clock_num
                    _ = employee.extend_call_clock_num
                    _ = employee.extend_in_turn_clock_num
                    _ = employee.extend_pointed_clock_num
                    _ = employee.in_turn_clock_num
                    _ = employee.main_clock_num
                    _ = employee.pointed_clock_num
                    _ = employee.small_clock_num
                    _ = employee.total_clock_num
                    _ = employee.total_item_num
                    _ = employee.date
                    _ = employee.created_at
                    _ = employee.updated_at
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return employees
            except SQLAlchemyError as e:
                logger.error(f"查询日期范围内员工数据失败: {e}")
                return []

    def search_employees_by_name(self, name: str, shop_id: Optional[int] = None, date: Optional[str] = None) -> List[Employee]:
        """根据员工姓名搜索员工"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Employee).filter(
                    Employee.employee_name.like(f'%{name}%')
                )
                
                if shop_id:
                    query = query.filter(Employee.shop_id == shop_id)
                
                if date:
                    query = query.filter(Employee.date == date)
                
                employees = query.order_by(Employee.employee_name).all()
                
                # 确保所有属性都已加载
                for employee in employees:
                    _ = employee.id
                    _ = employee.employee_id
                    _ = employee.employee_code
                    _ = employee.employee_name
                    _ = employee.shop_id
                    _ = employee.shop_name
                    _ = employee.additional_clock_num
                    _ = employee.call_clock_num
                    _ = employee.extend_additional_clock_num
                    _ = employee.extend_call_clock_num
                    _ = employee.extend_in_turn_clock_num
                    _ = employee.extend_pointed_clock_num
                    _ = employee.in_turn_clock_num
                    _ = employee.main_clock_num
                    _ = employee.pointed_clock_num
                    _ = employee.small_clock_num
                    _ = employee.total_clock_num
                    _ = employee.total_item_num
                    _ = employee.date
                    _ = employee.created_at
                    _ = employee.updated_at
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return employees
            except SQLAlchemyError as e:
                logger.error(f"搜索员工失败: {e}")
                return []

    def get_employee_statistics(self, date: str, shop_id: Optional[int] = None) -> Dict[str, Any]:
        """获取员工统计信息"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Employee).filter(Employee.date == date)
                
                if shop_id:
                    query = query.filter(Employee.shop_id == shop_id)
                
                employees = query.all()
                
                if not employees:
                    return {
                        'total_count': 0,
                        'total_work_hours': 0.0,
                        'avg_work_hours': 0.0,
                        'total_salary': 0.0,
                        'avg_salary': 0.0
                    }
                
                total_work_hours = sum(emp.work_hours or 0 for emp in employees)
                total_salary = sum(emp.salary or 0 for emp in employees)
                
                return {
                    'total_count': len(employees),
                    'total_work_hours': total_work_hours,
                    'avg_work_hours': round(total_work_hours / len(employees), 2),
                    'total_salary': total_salary,
                    'avg_salary': round(total_salary / len(employees), 2)
                }
            except SQLAlchemyError as e:
                logger.error(f"获取员工统计信息失败: {e}")
                return {}

    def get_employee_count(self, shop_id: Optional[int] = None) -> int:
        """获取员工总数"""
        with self.db_manager.get_session_context() as session:
            try:
                query = session.query(Employee)
                if shop_id:
                    query = query.filter(Employee.shop_id == shop_id)
                return query.count()
            except SQLAlchemyError as e:
                logger.error(f"获取员工总数失败: {e}")
                return 0

    def delete_employee_by_id(self, employee_id: int) -> bool:
        """根据员工ID删除员工记录"""
        with self.db_manager.get_session_context() as session:
            try:
                employee = session.query(Employee).filter(Employee.employee_id == employee_id).first()
                if employee:
                    session.delete(employee)
                    logger.info(f"成功删除员工记录: ID={employee_id}")
                    return True
                else:
                    logger.warning(f"未找到员工记录: ID={employee_id}")
                    return False
            except SQLAlchemyError as e:
                logger.error(f"删除员工记录失败: {e}")
                return False


# 创建全局实例
employee_dao = EmployeeDAO()