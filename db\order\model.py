# 订单数据模型

from sqlalchemy import Column, Integer, String, SmallInteger, TIMESTAMP, Text, DECIMAL, BigInteger, Index, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List, Dict, Any
from db.connection import Base

class Order(Base):
    """订单主表SQLAlchemy模型"""
    __tablename__ = 'orders'
    
    # 字段定义
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = Column(BigInteger, unique=True, nullable=False, comment='订单ID')
    order_display_id = Column(String(50), nullable=False, comment='订单显示ID')
    order_type = Column(SmallInteger, nullable=False, default=0, comment='订单类型')
    order_source = Column(SmallInteger, nullable=False, comment='订单来源')
    order_platform_source = Column(String(50), nullable=True, comment='订单平台来源')
    biz_type = Column(SmallInteger, nullable=False, comment='业务类型')
    
    # 金额相关
    amount = Column(DECIMAL(10, 2), nullable=False, comment='订单金额')
    amount_type = Column(Integer, nullable=False, default=0, comment='金额类型')
    payed_amount = Column(DECIMAL(10, 2), nullable=False, comment='已支付金额')
    pay_amount = Column(DECIMAL(10, 2), nullable=False, comment='实付金额')
    debt_amount = Column(DECIMAL(10, 2), nullable=False, default=0, comment='欠款金额')
    debt_mark = Column(SmallInteger, nullable=False, default=0, comment='欠款标记')
    remain_amount = Column(DECIMAL(10, 2), nullable=False, default=0, comment='剩余金额')
    refund_amount = Column(DECIMAL(10, 2), nullable=True, comment='退款金额')
    
    # 客户信息
    customer_name = Column(String(100), nullable=True, comment='客户姓名')
    membership_name = Column(String(100), nullable=True, comment='会员姓名')
    membership_no = Column(BigInteger, nullable=False, default=0, comment='会员号')
    mobile_no = Column(String(20), nullable=True, comment='手机号')
    
    # 订单状态和时间
    status = Column(SmallInteger, nullable=False, comment='订单状态')
    client_potential = Column(SmallInteger, nullable=False, default=0, comment='客户潜力')
    create_order_time = Column(TIMESTAMP, nullable=False, comment='创建订单时间')
    pay_time = Column(TIMESTAMP, nullable=True, comment='支付时间')
    refund_time = Column(TIMESTAMP, nullable=True, comment='退款时间')
    
    # 其他信息
    male_count = Column(SmallInteger, nullable=False, default=0, comment='男性数量')
    female_count = Column(SmallInteger, nullable=False, default=0, comment='女性数量')
    handwriting_order_no = Column(String(100), nullable=True, comment='手写订单号')
    is_record_order = Column(SmallInteger, nullable=False, default=0, comment='是否记录订单')
    is_refund = Column(SmallInteger, nullable=True, comment='是否退款')
    receipt_product_type = Column(String(50), nullable=True, comment='收据产品类型')
    refund_order_display_id = Column(String(50), nullable=True, comment='退款订单显示ID')
    remark = Column(Text, nullable=True, comment='备注')
    room_id = Column(Integer, nullable=False, default=0, comment='房间ID')
    room_name = Column(String(100), nullable=True, comment='房间名称')
    shop_id = Column(Integer, nullable=True, comment='店铺ID')
    appoint_id = Column(Integer, nullable=False, default=0, comment='预约ID')
    
    # 最后操作日志相关
    last_operation_employee_id = Column(Integer, nullable=True, comment='最后操作员工ID')
    last_operation_employee_name = Column(String(100), nullable=True, comment='最后操作员工姓名')
    last_operation_user_account_id = Column(BigInteger, nullable=True, comment='最后操作用户账户ID')
    last_operation_user_account_name = Column(String(100), nullable=True, comment='最后操作用户账户名')
    last_operation_time = Column(TIMESTAMP, nullable=True, comment='最后操作时间')
    last_operation_type = Column(SmallInteger, nullable=True, comment='最后操作类型')
    last_operation_type_name = Column(String(50), nullable=True, comment='最后操作类型名称')
    
    # 系统字段
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关联关系
    products = relationship("OrderProduct", back_populates="order", cascade="all, delete-orphan")
    payments = relationship("OrderPayment", back_populates="order", cascade="all, delete-orphan")
    promos = relationship("OrderPromo", back_populates="order", cascade="all, delete-orphan")
    artists = relationship("OrderArtist", back_populates="order", cascade="all, delete-orphan")
    
    # 索引定义
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_order_display_id', 'order_display_id'),
        Index('idx_shop_id', 'shop_id'),
        Index('idx_status', 'status'),
        Index('idx_create_time', 'create_order_time'),
        Index('idx_pay_time', 'pay_time'),
        Index('idx_mobile_no', 'mobile_no'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )
    
    @classmethod
    def from_api_data(cls, data: dict):
        """从API数据创建Order实例"""
        # 处理时间字段
        create_time = None
        pay_time = None
        refund_time = None
        
        if data.get('createOrderTime'):
            create_time = datetime.strptime(data['createOrderTime'], '%Y-%m-%d %H:%M:%S')
        if data.get('payTime'):
            pay_time = datetime.strptime(data['payTime'], '%Y-%m-%d %H:%M:%S')
        if data.get('refundTime'):
            refund_time = datetime.strptime(data['refundTime'], '%Y-%m-%d %H:%M:%S')
        
        # 处理最后操作日志
        last_op = data.get('lastOperationLog', {})
        last_op_time = None
        if last_op.get('operationTime'):
            last_op_time = datetime.strptime(last_op['operationTime'], '%Y-%m-%d %H:%M:%S')
        
        return cls(
            order_id=data.get('orderID'),
            order_display_id=data.get('orderDisplayID'),
            order_type=data.get('orderType', 0),
            order_source=data.get('orderSource'),
            order_platform_source=data.get('orderPlatformSource'),
            biz_type=data.get('bizType'),
            amount=data.get('amount', 0),
            amount_type=data.get('amountType', 0),
            payed_amount=data.get('payedAmount', 0),
            pay_amount=data.get('payAmount', 0),
            debt_amount=data.get('debtAmount', 0),
            debt_mark=data.get('debtMark', 0),
            remain_amount=data.get('remainAmount', 0),
            refund_amount=data.get('refundAmount'),
            customer_name=data.get('customerName'),
            membership_name=data.get('membershipName'),
            membership_no=data.get('membershipNO', 0),
            mobile_no=data.get('mobileNO'),
            status=data.get('status'),
            client_potential=1 if data.get('clientPotential') else 0,
            create_order_time=create_time,
            pay_time=pay_time,
            refund_time=refund_time,
            male_count=data.get('maleCount', 0),
            female_count=data.get('femaleCount', 0),
            handwriting_order_no=data.get('handwritingOrderNO'),
            is_record_order=1 if data.get('isRecordOrder') else 0,
            is_refund=data.get('isRefund'),
            receipt_product_type=data.get('receiptProductType'),
            refund_order_display_id=data.get('refundOrderDisplayID'),
            remark=data.get('remark'),
            room_id=data.get('roomID', 0),
            room_name=data.get('roomName'),
            shop_id=data.get('shopID'),
            appoint_id=data.get('appointID', 0),
            last_operation_employee_id=last_op.get('operationEmployeeID'),
            last_operation_employee_name=last_op.get('operationEmployeeName'),
            last_operation_user_account_id=last_op.get('operationUserAccountID'),
            last_operation_user_account_name=last_op.get('operationUserAccountName'),
            last_operation_time=last_op_time,
            last_operation_type=last_op.get('operationType'),
            last_operation_type_name=last_op.get('operationTypeName')
        )
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'order_display_id': self.order_display_id,
            'order_type': self.order_type,
            'order_source': self.order_source,
            'amount': float(self.amount) if self.amount else 0,
            'payed_amount': float(self.payed_amount) if self.payed_amount else 0,
            'pay_amount': float(self.pay_amount) if self.pay_amount else 0,
            'customer_name': self.customer_name,
            'mobile_no': self.mobile_no,
            'status': self.status,
            'create_order_time': self.create_order_time.isoformat() if self.create_order_time else None,
            'pay_time': self.pay_time.isoformat() if self.pay_time else None,
            'room_name': self.room_name,
            'shop_id': self.shop_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_id={self.order_id}, display_id='{self.order_display_id}', amount={self.amount})>"


class OrderProduct(Base):
    """订单商品表SQLAlchemy模型"""
    __tablename__ = 'order_products'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = Column(BigInteger, ForeignKey('orders.order_id'), nullable=False, comment='订单ID')
    order_sku_id = Column(BigInteger, nullable=False, comment='订单SKU ID')
    sku_id = Column(BigInteger, nullable=False, comment='SKU ID')
    sku_name = Column(String(255), nullable=False, comment='SKU名称')
    sku_type = Column(SmallInteger, nullable=False, comment='SKU类型')
    quantity = Column(DECIMAL(10, 2), nullable=False, comment='数量')
    quantity2 = Column(DECIMAL(10, 2), nullable=False, comment='数量2')
    deal_id = Column(BigInteger, nullable=True, comment='交易ID')
    extra_free_amount = Column(DECIMAL(10, 2), nullable=True, comment='额外免费金额')
    goupon_code = Column(String(100), nullable=True, comment='优惠券代码')
    is_add_clock_item = Column(SmallInteger, nullable=False, default=0, comment='是否加钟项目')
    total_promo_amount = Column(DECIMAL(10, 2), nullable=True, comment='总促销金额')
    stored_value_pay_amount = Column(DECIMAL(10, 2), nullable=True, comment='储值支付金额')
    to_sku_name = Column(String(255), nullable=True, comment='目标SKU名称')
    total_times = Column(Integer, nullable=True, comment='总次数')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    
    # 关联关系
    order = relationship("Order", back_populates="products")
    promos = relationship("OrderPromo", back_populates="product", cascade="all, delete-orphan")
    artists = relationship("OrderArtist", back_populates="product", cascade="all, delete-orphan")
    
    # 索引定义
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_order_sku_id', 'order_sku_id'),
        Index('idx_sku_id', 'sku_id'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )


class OrderPayment(Base):
    """订单支付详情表SQLAlchemy模型"""
    __tablename__ = 'order_payments'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = Column(BigInteger, ForeignKey('orders.order_id'), nullable=False, comment='订单ID')
    amount_type = Column(Integer, nullable=False, comment='金额类型')
    name = Column(String(100), nullable=False, comment='支付方式名称')
    pay_amount = Column(DECIMAL(10, 2), nullable=False, comment='支付金额')
    pay_status = Column(SmallInteger, nullable=False, comment='支付状态')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    
    # 关联关系
    order = relationship("Order", back_populates="payments")
    
    # 索引定义
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_amount_type', 'amount_type'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )


class OrderPromo(Base):
    """订单促销表SQLAlchemy模型"""
    __tablename__ = 'order_promos'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = Column(BigInteger, ForeignKey('orders.order_id'), nullable=False, comment='订单ID')
    order_sku_id = Column(BigInteger, ForeignKey('order_products.order_sku_id'), nullable=False, comment='订单SKU ID')
    promo_id = Column(BigInteger, nullable=False, comment='促销ID')
    promo_type = Column(SmallInteger, nullable=False, comment='促销类型')
    amount = Column(DECIMAL(10, 2), nullable=False, comment='促销金额')
    discount_id = Column(String(100), nullable=True, comment='折扣ID')
    discount = Column(DECIMAL(5, 4), nullable=True, comment='折扣')
    description = Column(Text, nullable=True, comment='描述')
    card_no = Column(String(100), nullable=True, comment='卡号')
    coupon_name = Column(String(255), nullable=True, comment='优惠券名称')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    
    # 关联关系
    order = relationship("Order", back_populates="promos")
    product = relationship("OrderProduct", back_populates="promos")
    
    # 索引定义
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_order_sku_id', 'order_sku_id'),
        Index('idx_promo_id', 'promo_id'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )


class OrderArtist(Base):
    """订单技师表SQLAlchemy模型"""
    __tablename__ = 'order_artists'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = Column(BigInteger, ForeignKey('orders.order_id'), nullable=False, comment='订单ID')
    order_sku_id = Column(BigInteger, ForeignKey('order_products.order_sku_id'), nullable=False, comment='订单SKU ID')
    artist_type = Column(String(20), nullable=False, comment='技师类型(artist/sales/assistant)')
    artist_id = Column(Integer, nullable=False, comment='技师ID')
    artist_name = Column(String(100), nullable=False, comment='技师姓名')
    artist_code = Column(String(50), nullable=True, comment='技师编码')
    is_artist_appoint = Column(SmallInteger, nullable=False, default=0, comment='是否指定技师')
    artist_work_mode = Column(String(50), nullable=True, comment='技师工作模式')
    add_clock = Column(String(50), nullable=True, comment='加钟')
    add_time = Column(BigInteger, nullable=True, comment='添加时间戳')
    assist_artist_id = Column(Integer, nullable=True, comment='助理技师ID')
    assist_artist_name = Column(String(100), nullable=True, comment='助理技师姓名')
    assist_artist_code = Column(String(50), nullable=True, comment='助理技师编码')
    is_assist_artist_appoint = Column(SmallInteger, nullable=True, comment='是否指定助理技师')
    assist_artist_work_mode = Column(String(50), nullable=True, comment='助理技师工作模式')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    
    # 关联关系
    order = relationship("Order", back_populates="artists")
    product = relationship("OrderProduct", back_populates="artists")
    
    # 索引定义
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
        Index('idx_order_sku_id', 'order_sku_id'),
        Index('idx_artist_id', 'artist_id'),
        Index('idx_artist_type', 'artist_type'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )