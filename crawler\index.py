import requests
import json
from config import get_logger

logger = get_logger(__name__)

base_url = "https://k.meituan.com/gateway"
mt_base_url = "https://e.dianping.com"
cookie = "uuid=817f3c6cffd2dea4c85a.**********.1.0.0; _lxsdk_cuid=196f1f3a927c8-02ac7a1b20b2468-26011f51-1fa400-196f1f3a927c8; _lxsdk=196f1f3a927c8-02ac7a1b20b2468-26011f51-1fa400-196f1f3a927c8; utm_source_rg=; e_b_id_352126=174f82bb41f180d8b5fd14d491fd492c; com.sankuai.meishi.fe.kdb-bsid=0PKEumgnoZNICqhRGabdBhPWMYs0v12Q5zq4gG9sbr6btUES1KturOtbVi_HgVCWTJOV2nxuQ026sR7bZi1bHA; _ga=GA1.1.633448494.**********; _lx_utm=utm_source%3Dgoogle%26utm_medium%3Dorganic; _ga_95GX0SH5GM=GS2.1.s1753443744$o2$g0$t1753443744$j60$l0$h0; lgk=f1f3a927c8-02ac7a1b20b2468-26011f51-1fa400-196f1f3a927c8||86400||196; WEBDFPID=wz497v04x68y5696zy27wxzw783xy1wx803u8u6y102979583uu57285-1753761070594-1747816003385AMKWSUAfd79fef3d01d5e9aadc18ccd4d0c95072761; saas-bsid=juC3Fq3HTk2u0Gn508LsiQhTIHGJ1jZWdXDV7eoWlYdCyAlDTLUH9d6nWQgb9QU6uMcFbCf-CypQhsHEWOeGiQ; pragma-token=juC3Fq3HTk2u0Gn508LsiQhTIHGJ1jZWdXDV7eoWlYdCyAlDTLUH9d6nWQgb9QU6uMcFbCf-CypQhsHEWOeGiQ; saas-rootshopid=80040; logan_custom_report=%7B%22unionId%22%3A%22196f1f3a927c8-02ac7a1b20b2468-26011f51-1fa400-196f1f3a927c8%22%2C%22biz%22%3A%22kmm%22%7D; kmmbsid=juC3Fq3HTk2u0Gn508LsiQhTIHGJ1jZWdXDV7eoWlYdCyAlDTLUH9d6nWQgb9QU6uMcFbCf-CypQhsHEWOeGiQ; melon_id=8435650168384742; saas-shopid=80040; orgNodeId=80040; pragma-shopid=80040; logan_session_token=u9nu798rvmqp3iutzwy0"
mt_cookie = "_gw_ab_call_15533_80=TRUE; _gw_ab_15533_80=735; _lxsdk_cuid=1984f8f8a57c8-0cf21526ac54c98-26011151-1fa400-1984f8f8a57c8; _lxsdk=1984f8f8a57c8-0cf21526ac54c98-26011151-1fa400-1984f8f8a57c8; WEBDFPID=1753681463909OISUOUMfd79fef3d01d5e9aadc18ccd4d0c95079452-1753681463909-1753681463909OISUOUMfd79fef3d01d5e9aadc18ccd4d0c95079452; utm_source_rg=; bizType=2; edper=0FoQV66pjqOj1KesiZ8sXD7OHgrrSR8EbQYHi53GPPxO7HaXiLZTSM0_-12xW9w1HeP5fHdHOTNTbQBfdeyDJg; logan_session_token=xww22qzffr35c6be2vsv; _hc.v=be645544-dfa5-dca5-2485-0cb17b07867c.1753681490; mpmerchant_portal_shopid=1323899336; merchantBookShopID=1323899336; merchantCategoryID=50405; _lxsdk_s=1984f8f8a58-5ea-49c-011%7C%7C53; _gw_ab_call_16029_168=TRUE; _gw_ab_16029_168=415; AWPTALOS4836="


def get_heders(is_mt: bool=False):
    return {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Cookie": mt_cookie if is_mt else cookie,
    }

# 检查响应
def check_res(res):
    if res["code"] != 0 and res['code'] != 200:
        return None
    return res["data"]

# 获取店铺列表
def get_shop_list():
    url = f"{base_url}/dzsaas/org/query"
    response = requests.get(url, headers=get_heders())
    return check_res(response.json())

# 获取店铺订单数据
def get_shop_order_data(shop_id,start_time,end_time,page_no,page_size):
    url = f"{base_url}/dzsaas/order/list/pc/query"
    params = {
        "tabStatus":10003,
        "keywordSearchStatus":124,
        "beginCreateTime":start_time,
        "endCreateTime":end_time,
        "pageNo": page_no,
        "pageSize": page_size,
        "orderSources":1,
        "sortField":1,
        "abnormalRecordOrder":0,
        "shopId":shop_id,
        "isSelectNoneEmployee":False
    }
    response = requests.get(url, headers=get_heders(), params=params)
    return check_res(response.json())


# 获取店铺员工上班数据
def get_shop_employee_data(shop_id,start_time,end_time):
    url = f"{base_url}/dzsaas/finance/report/query"
    params = {
        "orgNodeIds":shop_id,
        "startTimestamp":start_time,
        "endTimestamp":end_time,
        "componentId":5,
        "pageId":2,
        "pageNo":1,
        "pageSize":100,
    }
    response = requests.get(url, headers=get_heders(), params=params)
    res = check_res(response.json())
    if res and  len(res) > 0:
        return json.loads(res[0]['responseJson'])
    return None

# 获取订单提成数据
def get_shop_commission_data(order_id):
    url = f"https://k.meituan.com/dzsaas/order/benefit/query?orderId={order_id}"
    response = requests.get(url, headers=get_heders())
    return check_res(response.json())

# 获取店铺曝光数据
def get_shop_exposure_data(shop_id,date, platform):
    url = f"{mt_base_url}/gateway/mda/defaultData?pageType=flowAnalysis&yodaReady=h5&csecplatform=4&csecversion=3.2.1"
    # 构建x-www-form-urlencoded格式的参数字符串
    params_str = "&".join([f"{k}={v}" for k, v in {
        "date": f"{date},{date}",
        "shopIds": shop_id,
        "pageType": 'flowAnalysis',
        "device": 'pc',
        "source": 1,
        "platform": platform,
    }.items()])
    
    headers = get_heders(True)
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    response = requests.post(url, headers=headers, data=params_str)
    return check_res(response.json())

# 获取店铺交易数据
def get_shop_transaction_data(shop_id,date, platform):
    url = f"{mt_base_url}/mda/v5/trade?yodaReady=h5&csecplatform=4&csecversion=3.2.1"
    # 构建x-www-form-urlencoded格式的参数字符串
    params_str = "&".join([f"{k}={v}" for k, v in {
        "date": f"{date},{date}",
        "shopIds": shop_id,
        "pageType": 'v5Trade',
        "device": 'pc',
        "source": 1,
        "platform": platform,
    }.items()])
    
    headers = get_heders(True)
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    response = requests.post(url, headers=headers, data=params_str)
    return check_res(response.json())

# 获取店铺评价数据
def get_shop_evaluation_data(shop_id,date, platform):
    url = f"{mt_base_url}/mda/v5/estimate?yodaReady=h5&csecplatform=4&csecversion=3.2.1"
    # 构建x-www-form-urlencoded格式的参数字符串
    params_str = "&".join([f"{k}={v}" for k, v in {
        "date": f"{date},{date}",
        "shopIds": shop_id,
        "pageType": 'v5Estimate',
        "device": 'pc',
        "source": 1,
        "platform": platform,
    }.items()])
    
    headers = get_heders(True)
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    response = requests.post(url, headers=headers, data=params_str)
    return check_res(response.json())

# 获取店铺星级
def get_shop_star_data(shop_id,date, platform):
    url = f"{mt_base_url}/gateway/adviser/data?yodaReady=h5&csecplatform=4&csecversion=3.2.1"
    params_str = "&".join([f"{k}={v}" for k, v in {
        "date": f"{date},{date}",
        "shopIds": shop_id,
        "pageType": 'starInfoRealtime',
        "device": 'pc',
        "source": 1,
        "platform": platform,
    }.items()])
    
    headers = get_heders(True)
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    response = requests.post(url, headers=headers, data=params_str)
    return check_res(response.json())

