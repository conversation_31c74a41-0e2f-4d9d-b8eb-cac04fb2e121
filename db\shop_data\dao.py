from typing import List, Dict, Any
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_
from db.connection import db_manager
from .model import ShopData
import time

class ShopDataDAO:
    def __init__(self):
        pass
    
    def init_table(self):
        """初始化表"""
        db_manager.create_tables()
    
    def insert_or_update(self, mt_shop_id: str, date: str, platform: int, shop_id: int, shop_name: str, data: dict) -> dict:
        """插入或更新店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                # 查找是否存在相同的记录
                existing_record = session.query(ShopData).filter(
                    and_(
                        ShopData.mt_shop_id == mt_shop_id,
                        ShopData.date == date,
                        ShopData.platform == platform
                    )
                ).first()
                
                current_time = int(time.time() * 1000)
                
                if existing_record:
                    # 更新现有记录
                    for key, value in data.items():
                        if hasattr(existing_record, key):
                            setattr(existing_record, key, value)
                    existing_record.shop_id = shop_id
                    existing_record.shop_name = shop_name
                    existing_record.updated_at = current_time
                    return {'action': 'updated', 'id': existing_record.id}
                else:
                    # 创建新记录
                    shop_data = ShopData.from_api_data(mt_shop_id, date, platform, shop_id, shop_name, data)
                    session.add(shop_data)
                    return {'action': 'inserted', 'id': shop_data.id}
                    
            except SQLAlchemyError as e:
                raise e
    
    def batch_insert_or_update(self, records: List[dict]) -> dict:
        """批量插入或更新店铺数据"""
        with db_manager.get_session_context() as session:
            inserted_count = 0
            updated_count = 0
            
            try:
                for record in records:
                    mt_shop_id = record['mt_shop_id']
                    date = record['date']
                    platform = record['platform']
                    shop_id = record['shop_id']
                    shop_name = record['shop_name']
                    data = record['data']
                    
                    # 查找是否存在相同的记录
                    existing_record = session.query(ShopData).filter(
                        and_(
                            ShopData.mt_shop_id == mt_shop_id,
                            ShopData.date == date,
                            ShopData.platform == platform
                        )
                    ).first()
                    
                    current_time = int(time.time() * 1000)
                    
                    if existing_record:
                        # 更新现有记录
                        for key, value in data.items():
                            if hasattr(existing_record, key):
                                setattr(existing_record, key, value)
                        existing_record.shop_id = shop_id
                        existing_record.shop_name = shop_name
                        existing_record.updated_at = current_time
                        updated_count += 1
                    else:
                        # 创建新记录
                        shop_data = ShopData.from_api_data(mt_shop_id, date, platform, shop_id, shop_name, data)
                        session.add(shop_data)
                        inserted_count += 1
                
                return {'inserted': inserted_count, 'updated': updated_count}
                
            except SQLAlchemyError as e:
                raise e
    
    def get_shop_data_by_date(self, mt_shop_id: str, date: str, platform: int = None) -> List[ShopData]:
        """根据店铺ID和日期获取店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                query = session.query(ShopData).filter(
                    and_(
                        ShopData.mt_shop_id == mt_shop_id,
                        ShopData.date == date
                    )
                )
                
                if platform is not None:
                    query = query.filter(ShopData.platform == platform)
                
                shop_data_list = query.all()
                
                # 确保所有属性都已加载（使用反射方式）
                for shop_data in shop_data_list:
                    # 通过反射访问所有列属性
                    for column in ShopData.__table__.columns:
                        _ = getattr(shop_data, column.name)
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return shop_data_list
            except SQLAlchemyError as e:
                raise e

    def get_shop_data_by_date_range(self, mt_shop_id: str, start_date: str, end_date: str, platform: int = None) -> List[ShopData]:
        """根据店铺ID和日期范围获取店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                query = session.query(ShopData).filter(
                    and_(
                        ShopData.mt_shop_id == mt_shop_id,
                        ShopData.date >= start_date,
                        ShopData.date <= end_date
                    )
                )
                
                if platform is not None:
                    query = query.filter(ShopData.platform == platform)
                
                shop_data_list = query.order_by(ShopData.date.desc()).all()
                
                # 确保所有属性都已加载（使用反射方式）
                for shop_data in shop_data_list:
                    # 通过反射访问所有列属性
                    for column in ShopData.__table__.columns:
                        _ = getattr(shop_data, column.name)
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return shop_data_list
            except SQLAlchemyError as e:
                raise e

    def get_all_shop_data_by_date(self, date: str, platform: int = None) -> List[ShopData]:
        """根据日期获取所有店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                query = session.query(ShopData).filter(ShopData.date == date)
                
                if platform is not None:
                    query = query.filter(ShopData.platform == platform)
                
                shop_data_list = query.all()
                
                # 确保所有属性都已加载（使用反射方式）
                for shop_data in shop_data_list:
                    # 通过反射访问所有列属性
                    for column in ShopData.__table__.columns:
                        _ = getattr(shop_data, column.name)
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return shop_data_list
            except SQLAlchemyError as e:
                raise e

    def get_shop_data_statistics(self, mt_shop_id: str, start_date: str, end_date: str, platform: int = None) -> dict:
        """获取店铺数据统计信息"""
        with db_manager.get_session_context() as session:
            try:
                query = session.query(ShopData).filter(
                    and_(
                        ShopData.mt_shop_id == mt_shop_id,
                        ShopData.date >= start_date,
                        ShopData.date <= end_date
                    )
                )
                
                if platform is not None:
                    query = query.filter(ShopData.platform == platform)
                
                records = query.all()
                
                if not records:
                    return {
                        'total_records': 0,
                        'date_range': f"{start_date} ~ {end_date}",
                        'total_orders': 0,
                        'total_reviews': 0,
                        'total_revenue': 0.0,
                        'avg_star': 0.0
                    }
                
                total_orders = sum(record.order_cnt for record in records)
                total_reviews = sum(record.review_new_cnt for record in records)
                total_revenue = sum(record.verify_price for record in records)
                avg_star = sum(record.star for record in records) / len(records) if records else 0.0
                
                return {
                    'total_records': len(records),
                    'date_range': f"{start_date} ~ {end_date}",
                    'total_orders': total_orders,
                    'total_reviews': total_reviews,
                    'total_revenue': total_revenue,
                    'avg_star': round(avg_star, 2)
                }
                
            except SQLAlchemyError as e:
                raise e

    def delete_shop_data(self, mt_shop_id: str, date: str, platform: int) -> bool:
        """删除店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                record = session.query(ShopData).filter(
                    and_(
                        ShopData.mt_shop_id == mt_shop_id,
                        ShopData.date == date,
                        ShopData.platform == platform
                    )
                ).first()
                
                if record:
                    session.delete(record)
                    return True
                return False
                
            except SQLAlchemyError as e:
                raise e

# 创建全局实例
shop_data_dao = ShopDataDAO()