# 店铺数据模型

from sqlalchemy import Column, Integer, String, SmallInteger, TIMESTAMP, DECIMAL, Index
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional
from db.connection import Base

class Shop(Base):
    """店铺SQLAlchemy模型"""
    __tablename__ = 'shops'
    
    # 字段定义
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    org_node_id = Column(Integer, unique=True, nullable=False, comment='组织节点ID')
    name = Column(String(255), nullable=False, comment='店铺名称')
    type = Column(SmallInteger, nullable=False, comment='店铺类型')
    shop_id = Column(Integer, nullable=True, comment='店铺ID')
    mt_shop_id = Column(String(255), comment='美团店铺ID')
    chain_type = Column(SmallInteger, nullable=True, comment='连锁类型')
    status = Column(SmallInteger, nullable=False, default=1, comment='状态')
    lng = Column(String(255), nullable=True, comment='经度')
    lat = Column(String(255), nullable=True, comment='纬度')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')
    
    # 索引定义
    __table_args__ = (
        Index('idx_org_node_id', 'org_node_id'),
        Index('idx_shop_id', 'shop_id'),
        Index('idx_status', 'status'),
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
    )
    
    def __init__(self, org_node_id: int, name: str, type: int, shop_id: Optional[int] = None, 
                 chain_type: Optional[int] = None, status: int = 1, lng: Optional[str] = None, 
                 lat: Optional[str] = None):
        self.org_node_id = org_node_id
        self.name = name
        self.type = type
        self.shop_id = shop_id
        self.chain_type = chain_type
        self.status = status
        self.lng = lng
        self.lat = lat
    
    @classmethod
    def from_api_data(cls, data: dict):
        """从API数据创建Shop实例"""
        return cls(
            org_node_id=data.get('orgNodeId'),
            name=data.get('name'),
            type=data.get('type'),
            shop_id=data.get('shopId'),
            chain_type=data.get('chainType'),
            status=data.get('status', 1),
            lng=data.get('lng'),
            lat=data.get('lat')
        )
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'org_node_id': self.org_node_id,
            'name': self.name,
            'type': self.type,
            'shop_id': self.shop_id,
            'mt_shop_id': self.mt_shop_id,
            'chain_type': self.chain_type,
            'status': self.status,
            'lng': self.lng,
            'lat': self.lat,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def __repr__(self):
        return f"<Shop(id={self.id}, name='{self.name}', org_node_id={self.org_node_id}, shop_id={self.shop_id})>"
    
    def __str__(self):
        return f"Shop(id={self.id}, name='{self.name}', shop_id={self.shop_id})"