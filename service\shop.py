# 店铺数据同步服务

import json
from crawler.index import  get_shop_employee_data, get_shop_evaluation_data, get_shop_exposure_data, get_shop_transaction_data, get_shop_star_data
from db.shop import shop_dao, Shop
from db.employee import employee_dao
from db.shop_data import shop_data_dao
from db.order import order_dao
from db.commission import commission_dao
from db.shop_analysis import shop_analysis_dao
from service.order import sync_shop_orders
from common.utils import parse_number
from config import get_logger
from datetime import datetime, timedelta
import time

# 获取日志器
log = get_logger(__name__)
        
# 线下支付方式列表
offline_payment_methods = (
    '客满满收款',
    '富掌柜',
    '扫码收款-微信',
    '扫码收款-支付宝', 
    '扫码收款-银联',
    '现金',
    '美团收款码-微信',
    '美团收款码-支付宝',
    '美团收款码-银联'
) 
# 线上支付方式列表
online_payment_methods = (
    '抖音',
    '美团',
    '抖音验券',
    '美团验券' 
)

def sync_shop():
    """同步店铺数据到数据库"""
    log.info("开始同步店铺数据...")
    
    # 处理店铺数据
    shop_records = []
    total_shops = 0
    log.debug(f"原始店铺数据: {json.dumps(shop_data, ensure_ascii=False)}")
    
    # 从API返回的数据结构中提取org信息
    org_data = shop_data.get('org', {})
    if org_data.get('type') == 1:  # 总部类型为1
        child_orgs = org_data.get('childOrgs', [])
        for child_org in child_orgs:
            shop_record = (
                child_org.get('orgNodeId'),
                child_org.get('name'),
                child_org.get('type'),
                child_org.get('shopId'),
                child_org.get('chainType'),
                child_org.get('status', 1)
            )
            shop_records.append(shop_record)
            total_shops += 1
    
    if not shop_records:
        log.warning("没有找到需要同步的店铺数据")
        return False
    
    log.info(f"找到 {total_shops} 个店铺，开始同步到数据库...")
    
    # 批量插入或更新数据
    return shop_dao.batch_insert_or_update(shop_records)
    
def sync_shop_employee(shop_id: int, date: str):
    """同步店铺员工数据到数据库
    
    Args:
        shop_id: 店铺ID
        date: 数据日期，格式：YYYY-MM-DD，如 '2025-01-26'
    """
    log.info(f"开始同步店铺员工数据 - 店铺ID: {shop_id}, 日期: {date}")

    total_inserted = 0
    total_updated = 0
    total_errors = 0
    
    try:
        # 解析日期
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        
        # 时间要从当天凌晨6点到第二天凌晨6点
        # 当天开始时间（06:00:00）
        start_datetime = date_obj.replace(hour=6, minute=0, second=0, microsecond=0)
        start_time = int(start_datetime.timestamp() * 1000)  # 转换为毫秒时间戳
        

        # 第二天结束时间（05:59:59）
        end_datetime = (date_obj + timedelta(days=1)).replace(hour=5, minute=59, second=59, microsecond=999000)
        end_time = int(end_datetime.timestamp() * 1000)  # 转换为毫秒时间戳
        log.info(f"时间范围: {start_datetime} 到 {end_datetime}")
        
        # 获取员工数据
        employee_data = get_shop_employee_data(shop_id, start_time, end_time)
        
        if not employee_data:
            log.warning("返回的员工数据为空")
            return
        
        if not isinstance(employee_data, list):
            log.warning("返回的员工数据格式不正确")
            return
        
        log.info(f"获取到 {len(employee_data)} 条员工数据")
        
        # 批量插入或更新员工数据
        result = employee_dao.batch_insert_or_update(employee_data, date)
        
        total_inserted += result['inserted']
        total_updated += result['updated']
        total_errors += result['errors']
        
        log.info(f"处理完成: 新增 {result['inserted']}, 更新 {result['updated']}, 错误 {result['errors']}")
        
    except ValueError as e:
        log.error(f"日期格式错误: {e}")
        log.info("请使用正确的日期格式，如: '2025-01-26'")
        total_errors += 1
    except Exception as e:
        log.error(f"处理店铺 {shop_id} 时出错: {e}")
        total_errors += 1
        
    log.info("员工数据同步完成!")
    log.info(f"处理统计: 新增员工记录: {total_inserted}, 更新员工记录: {total_updated}, 错误数量: {total_errors}")

def sync_shop_all_data(mt_shop_id:str, date: str, platform:int,shop_id:int,shop_name: str):
    """同步店铺所有数据"""
    # platform 1：点评，2：美团
    shop_evaluation_list = get_shop_evaluation_data(mt_shop_id,date,platform)
    shop_exposure_list = get_shop_exposure_data(mt_shop_id,date,platform)
    shop_transaction_list = get_shop_transaction_data(mt_shop_id,date,platform)
    shop_star_list = get_shop_star_data(mt_shop_id,date,platform)
    
    review_new_cnt = 0 # 新增评价数量
    bad_review_new_cnt = 0 # 新增差评数量
    good_review_new_cnt = 0 # 新增好评数量
    order_cnt = 0 # 下单人数
    order_coupon_cnt = 0 # 下单券数
    order_original_price = 0 # 下单金额(原价)
    order_price = 0 # 下单金额
    verify_cnt = 0 # 核销人数
    verify_coupon_cnt = 0 # 核销劵数
    verify_original_price = 0 # 核销金额（原价）
    verify_price = 0 # 核销金额
    refund_cnt = 0 # 退款劵数
    refund_original_price = 0 # 退款金额（原价）
    refund_price = 0 # 退款金额
    exposure_cnt = 0 # 曝光人数
    exposure_count_cnt = 0  # 曝光次数
    exposure_visit_cnt = 0 # 访问人数
    exposure_visit_count_cnt = 0  # 访问次数
    exposure_visit_rate = 0 # 访问率
    intent_cnt = 0 # 意向转化人数
    intent_rate = 0 # 意向转化率
    leave_cnt = 0 # 留资人数
    collect_cnt = 0 # 新增收藏人数
    check_cnt = 0  # 新增打卡人数
    star = 0.0 # 店铺星级
    
    # 评价数据
    if shop_evaluation_list:
        for item in shop_evaluation_list:
            if item['componentId'] == 'reviewOptionSummaryPC':
                for sub_item in item['body']:
                    if sub_item['variable'] == 'review_new_cnt':
                        review_new_cnt = parse_number(sub_item['value'])
                    if sub_item['variable'] == 'bad_review_new_cnt':
                        bad_review_new_cnt = parse_number(sub_item['value'])
                break
    good_review_new_cnt = review_new_cnt - bad_review_new_cnt

    # 交易数据
    if shop_transaction_list:
        for item in shop_transaction_list:
            if item['componentId'] == 'tradeOptionSummaryPC':
                for sub_item in item['body']:
                    if sub_item['name'] == '下单人数':
                        order_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '下单券数':
                        order_coupon_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '下单金额（原价）':
                        order_original_price = parse_number(sub_item['value'])
                    elif sub_item['name'] == '下单金额':
                        order_price = parse_number(sub_item['value'])
                    elif sub_item['name'] == '核销人数':
                        verify_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '核销劵数':
                        verify_coupon_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '核销金额（原价）':
                        verify_original_price = parse_number(sub_item['value'])
                    elif sub_item['name'] == '核销金额':
                        verify_price = parse_number(sub_item['value'])
                    elif sub_item['name'] == '退款劵数':
                        refund_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '退款金额（原价）':
                        refund_original_price = parse_number(sub_item['value'])
                    elif sub_item['name'] == '退款金额':
                        refund_price = parse_number(sub_item['value'])
                break
        
    # 曝光数据
    if shop_exposure_list:
        for item in shop_exposure_list:
            if item['componentId'] == 'flowDataSummaryPC':
                for sub_item in item['body']['data']:
                    if sub_item['name'] == '曝光人数':
                        exposure_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '曝光次数':
                        exposure_count_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '访问人数':
                        exposure_visit_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '访问次数':
                        exposure_visit_count_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '曝光访问转化率':
                        exposure_visit_rate = parse_number(sub_item['value'])
                    elif sub_item['name'] == '意向转化人数':
                        intent_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '意向转化率':
                        intent_rate = parse_number(sub_item['value'])
                    elif sub_item['name'] == '下单人数':
                        order_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '留资人数':
                        leave_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '新增收藏人数':
                        collect_cnt = parse_number(sub_item['value'])
                    elif sub_item['name'] == '新增打卡人数':
                        check_cnt = parse_number(sub_item['value'])


                break
    
    # 星级数据
    if shop_star_list:
        for item in shop_star_list:
            if item['componentId'] == 'starRealtimeSummary':
                for sub_item in item['body']:
                    if sub_item['variable'] == 'dp_shop_fivescore' or sub_item['variable'] == 'mt_shop_fivescore':
                        star = parse_number(sub_item['value'])
                        break
                break
        
    # 构建数据字典
    shop_data = {
        'review_new_cnt': review_new_cnt,
        'bad_review_new_cnt': bad_review_new_cnt,
        'good_review_new_cnt': good_review_new_cnt,
        'order_cnt': order_cnt,
        'order_coupon_cnt': order_coupon_cnt,
        'order_original_price': order_original_price,
        'order_price': order_price,
        'verify_cnt': verify_cnt,
        'verify_coupon_cnt': verify_coupon_cnt,
        'verify_original_price': verify_original_price,
        'verify_price': verify_price,
        'refund_cnt': refund_cnt,
        'refund_original_price': refund_original_price,
        'refund_price': refund_price,
        'exposure_cnt': exposure_cnt,
        'exposure_count_cnt': exposure_count_cnt,
        'exposure_visit_cnt': exposure_visit_cnt,
        'exposure_visit_count_cnt': exposure_visit_count_cnt,
        'exposure_visit_rate': exposure_visit_rate,
        'intent_cnt': intent_cnt,
        'intent_rate': intent_rate,
        'leave_cnt': leave_cnt,
        'collect_cnt': collect_cnt,
        'check_cnt': check_cnt,
        'star': star
    }
    
    try:
        # 存储到数据库
        result = shop_data_dao.insert_or_update(mt_shop_id, date, platform, shop_id, shop_name, shop_data)
        
        log.info("店铺数据同步成功:")
        log.info(f"美团店铺ID: {mt_shop_id}, 日期: {date}, 平台: {'点评' if platform == 1 else '美团'}")
        log.info(f"店铺名称: {shop_name}, 操作: {result['action']}, 记录ID: {result['id']}")
        
        # 显示关键数据
        log.info("关键数据:")
        log.info(f"新增评价: {review_new_cnt} (好评: {good_review_new_cnt}, 差评: {bad_review_new_cnt})")
        log.info(f"下单人数: {order_cnt}, 下单金额: {order_price}")
        log.info(f"核销人数: {verify_cnt}, 核销金额: {verify_price}")
        log.info(f"曝光人数: {exposure_cnt}, 访问人数: {exposure_visit_cnt}")
        log.info(f"店铺星级: {star}")
        
        return result
        
    except Exception as e:
        log.error(f"店铺数据存储失败: {e}")
        raise e

def get_all_shop():
    """获取所有店铺信息"""
    return shop_dao.get_all_shops()

def sync_data(date: str, isToday: bool = False):
    """同步店铺数据 
    美团那边的数据只能统计到昨天，所以如果是今天的话，就没必要同步了
    Args:
        date: 日期
        isToday: 是否是今天
    """
    shop_list = get_all_shop()
    for shop in shop_list:
        log.info(f"开始同步店铺 {shop.name} 的数据")
        # 同步美团、大众点评相关数据
        if shop.mt_shop_id and not isToday:
            sync_shop_all_data(shop.mt_shop_id, date, 1, shop.shop_id, shop.name)
            sync_shop_all_data(shop.mt_shop_id, date, 2, shop.shop_id, shop.name)

        # 同步员工数据,改为从订单数据中分析
        # sync_shop_employee(shop.shop_id, date)
        
        # 同步订单数据
        # 解析日期
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        start_datetime = date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
        start_time = int(start_datetime.timestamp() * 1000)  # 转换为毫秒时间戳
        end_datetime = date_obj.replace(hour=23, minute=59, second=59, microsecond=999000)
        end_time = int(end_datetime.timestamp() * 1000)  # 转换为毫秒时间戳
        sync_shop_orders(shop.shop_id, start_time, end_time, 20)
        
        log.info(f"完成同步店铺 {shop.name} 的数据")
        time.sleep(0.5)

# 分析店铺数据
def analyze_shop_data(date: str):
    """分析店铺数据"""
    shop_list = get_all_shop()
    for shop in shop_list:
        log.info(f"分析店铺 {shop.name} 的数据")
        # 分析店铺数据
        analyze_shop(shop, date)
        log.info(f"分析完成店铺 {shop.name} 的数据")
        
def analyze_shop(shop, date: str):
    """分析店铺数据"""
    log.info(f"分析店铺 {shop.name} 的数据")
    
    # ===============  定义需要存储的变量 ======================
    mt_exposure_cnt = 0  # 美团曝光人数
    dp_exposure_cnt = 0  # 点评曝光人数
    all_exposure_cnt = 0  # 总曝光人数
    all_intent_cnt = 0   # 意向转化人数
    mt_intent_cnt = 0   # 美团意向转化人数
    dp_intent_cnt = 0   # 点评意向转化人数
    all_visit_cnt = 0   # 总访问人数
    mt_visit_cnt = 0   # 美团访问人数
    dp_visit_cnt = 0   # 点评访问人数
    good_review_new_cnt = 0 # 新增好评数量
    bad_review_new_cnt = 0 # 新增差评数量
    order_coupon_cnt = 0 # 下单劵数
    verify_coupon_cnt = 0  # 核销劵数
    refund_coupon_cnt = 0 # 退款劵数
    working_count = 0 # 上班人数
    dp_star = 0 # 点评星级
    mt_star = 0 # 美团星级
    flow_cnt = 0 # 客流
    
    # 总营业额，总现金流
    total_revenue = 0 # 总营业额 线上+线下+耗卡
    total_cash_flow = 0 # 总现金流  线上+线下+卡金
    
    all_card_amount = 0 # 卡金金额
    card_amount = 0 # 办卡金额
    recharge_amount = 0 # 充值金额
    cost_card_amount = 0 # 耗卡金额
    offline_pay_amount = 0 # 线下支付金额
    online_pay_amount = 0 # 线上支付金额
    mt_amount = 0 # 美团金额
    dy_amount = 0 # 抖音金额
    
    total_commission = 0.0 # 总提成金额
    sales_commission = 0.0  # 销售提成金额
    service_commission = 0.0 # 技师提成金额
    
    working_count = 0  # 上班的员工数量
    
    # ===============  临时变量 ======================
    working_employees = set() # 上班员工列表
    
    # ===============  获取数据 ======================
    # 获取该店铺该日期的订单数据
    # 将日期字符串转换为datetime对象
    # 一天的时间从凌晨6点算起，如7月26日的订单是从7月26日6点到7月27日6点前
    date_obj = datetime.strptime(date, '%Y-%m-%d')
    start_datetime = date_obj.replace(hour=6, minute=0, second=0, microsecond=0)
    # 结束时间是次日凌晨6点前（即次日5点59分59秒）
    next_day = date_obj + timedelta(days=1)
    end_datetime = next_day.replace(hour=5, minute=59, second=59, microsecond=999000)
    orders = order_dao.get_orders_by_date_range(
        start_date=start_datetime,
        end_date=end_datetime,
        shop_id=shop.shop_id
    )
    log.info(f"获取到 {len(orders)} 条订单数据")
    # 处理订单数据
    for order in orders:
        if order.status == 15:
            log.info("跳过退款订单")
            continue
        
        for payment in order.payments:
            # 只统计正常的订单
            if payment.pay_status == 2:
                # 统计办卡金额，order_type为４就是办卡 充卡是5. 都算到卡金里
                if order.order_type == 4 :
                    card_amount += payment.pay_amount
                    all_card_amount += payment.pay_amount
                elif order.order_type == 5:
                    recharge_amount += payment.pay_amount
                    all_card_amount += payment.pay_amount
                else:
                    # 统计线下现金
                    if payment.name in offline_payment_methods:
                        offline_pay_amount += payment.pay_amount
                    # 线上支付
                    elif payment.name in online_payment_methods:
                        # 总抖音金额
                        if payment.name == '抖音' or payment.name == '抖音验券':
                            dy_amount += payment.pay_amount
                        # 总美团金额
                        else:
                            mt_amount += payment.pay_amount
                        # 总线上金额
                        online_pay_amount += payment.pay_amount
                    # 统计耗卡金额
                    elif payment.name == '会员卡':
                        cost_card_amount += payment.pay_amount
        
        # 统计客流，不重复的技师算一个客流
        artist_ids = set()
        for artist in order.artists:
            if artist.artist_type == 'artist':
                artist_ids.add(artist.artist_id)
                working_employees.add(artist.artist_id)
        flow_cnt += len(artist_ids)

    # 统计总营业额 线上+线下+耗卡
    total_revenue = offline_pay_amount + online_pay_amount + cost_card_amount
    # 总现金流  线下+线上+卡金
    total_cash_flow = offline_pay_amount + online_pay_amount + all_card_amount
    
    # 从数据库中获取订单提成数据
    commission = commission_dao.get_commission_statistics(shop.shop_id, date)
    total_commission = commission['total_commission']
    sales_commission = commission['sales_commission']
    service_commission = commission['service_commission']
        
    # 从DB中获取店铺该日期的shop_data数据
    shop_data_list = shop_data_dao.get_shop_data_by_date(
        mt_shop_id=str(shop.mt_shop_id), 
        date=date
    )
    
    for shop_data in shop_data_list:
        # 美团
        if shop_data.platform == 1:
            mt_exposure_cnt = shop_data.exposure_cnt
            mt_star = shop_data.star
            mt_intent_cnt = shop_data.intent_cnt
            mt_visit_cnt = shop_data.exposure_visit_cnt
        # 点评
        elif shop_data.platform == 2:
            dp_exposure_cnt = shop_data.exposure_cnt
            dp_star = shop_data.star
            dp_intent_cnt = shop_data.intent_cnt
            dp_visit_cnt = shop_data.exposure_visit_cnt
            
        # 好评数
        good_review_new_cnt += shop_data.good_review_new_cnt
        # 差评数
        bad_review_new_cnt += shop_data.bad_review_new_cnt
        # 下单劵数
        order_coupon_cnt += shop_data.order_coupon_cnt
        # 核销劵数
        verify_coupon_cnt += shop_data.verify_coupon_cnt
        # 退款劵数
        refund_coupon_cnt += shop_data.refund_cnt
        # 意向转化人数
        all_intent_cnt += shop_data.intent_cnt
        # 总访问人数
        all_visit_cnt += shop_data.exposure_visit_cnt
        # 总曝光人数
        all_exposure_cnt += shop_data.exposure_cnt

    # 统计上班员工数量
    working_count = len(working_employees)

    
    # # 打印输出要保存到数据库中的数据
    # log.info("=" * 80)
    # log.info(f"📊 【{shop.name}】{date} 数据分析报告")
    # log.info("=" * 80)
    
    # # 营业数据
    # log.info("💰 营业数据:")
    # log.info(f"   总营业额: ¥{total_revenue:,.2f} (线上+线下+耗卡)")
    # log.info(f"   总现金流: ¥{total_cash_flow:,.2f} (线上+线下+卡金)")
    # log.info(f"   线上支付: ¥{online_pay_amount:,.2f}")
    # log.info(f"   线下支付: ¥{offline_pay_amount:,.2f}")
    # log.info(f"   耗卡金额: ¥{cost_card_amount:,.2f}")
    
    # # 卡金数据
    # log.info("💳 卡金数据:")
    # log.info(f"   总卡金: ¥{all_card_amount:,.2f}")
    # log.info(f"   办卡金额: ¥{card_amount:,.2f}")
    # log.info(f"   充值金额: ¥{recharge_amount:,.2f}")
    
    # # 平台数据
    # log.info("🛒 平台数据:")
    # log.info(f"   美团金额: ¥{mt_amount:,.2f}")
    # log.info(f"   抖音金额: ¥{dy_amount:,.2f}")
    
    # # 提成数据
    # log.info("💼 提成数据:")
    # log.info(f"   总提成: ¥{total_commission:,.2f}")
    # log.info(f"   销售提成: ¥{sales_commission:,.2f}")
    # log.info(f"   技师提成: ¥{service_commission:,.2f}")
    
    # # 流量数据
    # log.info("📈 流量数据:")
    # log.info(f"   总曝光人数: {all_exposure_cnt:,}人 (美团: {mt_exposure_cnt:,}, 点评: {dp_exposure_cnt:,})")
    # log.info(f"   总访问人数: {all_visit_cnt:,}人 (美团: {mt_visit_cnt:,}, 点评: {dp_visit_cnt:,})")
    # log.info(f"   意向转化人数: {all_intent_cnt:,}人 (美团: {mt_intent_cnt:,}, 点评: {dp_intent_cnt:,})")
    # log.info(f"   客流量: {flow_cnt:,}人")
    
    # # 评价数据
    # log.info("⭐ 评价数据:")
    # log.info(f"   新增好评: {good_review_new_cnt:,}条")
    # log.info(f"   新增差评: {bad_review_new_cnt:,}条")
    # log.info(f"   美团星级: {mt_star}⭐")
    # log.info(f"   点评星级: {dp_star}⭐")
    
    # # 券数据
    # log.info("🎫 券数据:")
    # log.info(f"   下单券数: {order_coupon_cnt:,}张")
    # log.info(f"   核销券数: {verify_coupon_cnt:,}张")
    # log.info(f"   退款券数: {refund_coupon_cnt:,}张")
    
    # # 员工数据
    # log.info("👥 员工数据:")
    # log.info(f"   上班人数: {working_count:,}人")
    
    log.info("=" * 80)
    log.info(f"✅ 【{shop.name}】数据分析完成")
    log.info("=" * 80)
    
    # 保存分析数据到数据库
    analysis_data = {
        'shop_id': shop.shop_id,
        'shop_name': shop.name,
        'analysis_date': datetime.strptime(date, '%Y-%m-%d').date(),
        'total_revenue': total_revenue,
        'total_cash_flow': total_cash_flow,
        'online_pay_amount': online_pay_amount,
        'offline_pay_amount': offline_pay_amount,
        'cost_card_amount': cost_card_amount,
        'all_card_amount': all_card_amount,
        'card_amount': card_amount,
        'recharge_amount': recharge_amount,
        'mt_amount': mt_amount,
        'dy_amount': dy_amount,
        'total_commission': total_commission,
        'sales_commission': sales_commission,
        'service_commission': service_commission,
        'all_exposure_cnt': all_exposure_cnt,
        'mt_exposure_cnt': mt_exposure_cnt,
        'dp_exposure_cnt': dp_exposure_cnt,
        'all_visit_cnt': all_visit_cnt,
        'mt_visit_cnt': mt_visit_cnt,
        'dp_visit_cnt': dp_visit_cnt,
        'all_intent_cnt': all_intent_cnt,
        'mt_intent_cnt': mt_intent_cnt,
        'dp_intent_cnt': dp_intent_cnt,
        'flow_cnt': flow_cnt,
        'good_review_new_cnt': good_review_new_cnt,
        'bad_review_new_cnt': bad_review_new_cnt,
        'mt_star': mt_star,
        'dp_star': dp_star,
        'order_coupon_cnt': order_coupon_cnt,
        'verify_coupon_cnt': verify_coupon_cnt,
        'refund_coupon_cnt': refund_coupon_cnt,
        'working_count': working_count
    }
    
    # 保存到数据库
    if shop_analysis_dao.save_analysis_data(analysis_data):
        log.info(f"💾 【{shop.name}】{date} 分析数据已保存到数据库")
    else:
        log.error(f"❌ 【{shop.name}】{date} 分析数据保存失败")
    