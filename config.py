# 配置文件
import logging
import logging.handlers
import os
from datetime import datetime

# 加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有安装python-dotenv，忽略错误
    pass

# 数据库配置
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', '***************'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'mysql_WYbrHc'),
    'database': os.getenv('DB_NAME', 'fsy'),
    'charset': 'utf8mb4'
}

# API配置
API_CONFIG = {
    'base_url': 'https://k.meituan.com/gateway',
    'timeout': 30
}

# 日志配置
LOG_CONFIG = {
    'level': logging.INFO,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S',
    'log_dir': 'logs',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

def setup_logging():
    """设置日志配置"""
    # 避免重复配置
    root_logger = logging.getLogger()
    if root_logger.handlers:
        return
    
    # 创建日志目录
    if not os.path.exists(LOG_CONFIG['log_dir']):
        os.makedirs(LOG_CONFIG['log_dir'])
    
    # 配置根日志器
    logging.basicConfig(
        level=LOG_CONFIG['level'],
        format=LOG_CONFIG['format'],
        datefmt=LOG_CONFIG['date_format'],
        handlers=[
            # 控制台处理器
            logging.StreamHandler(),
            # 文件处理器
            logging.handlers.RotatingFileHandler(
                filename=os.path.join(LOG_CONFIG['log_dir'], f'fsy_{datetime.now().strftime("%Y%m%d")}.log'),
                maxBytes=LOG_CONFIG['max_bytes'],
                backupCount=LOG_CONFIG['backup_count'],
                encoding='utf-8'
            )
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # 确保uvicorn日志也使用我们的配置
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('uvicorn.access').setLevel(logging.INFO)

def get_logger(name):
    """获取日志器"""
    return logging.getLogger(name)