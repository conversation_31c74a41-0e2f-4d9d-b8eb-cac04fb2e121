"""
API路由定义
"""
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from datetime import datetime
from config import get_logger
from .sync import router as sync_router
from .analysis import router as analysis_router
from .scheduler import router as scheduler_router
import os

logger = get_logger(__name__)



def create_app():
    """创建FastAPI应用实例"""
    # 确保日志配置在uvicorn进程中也生效
    from config import setup_logging
    setup_logging()
    
    app = FastAPI(
        title="FSY数据同步API",
        description="店铺数据同步API接口",
        version="1.0.0"
    )
    
    # 导入并注册路由
    app.include_router(sync_router)
    app.include_router(analysis_router)
    app.include_router(scheduler_router)
    
    # 挂载静态文件服务
    html_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "html")
    if os.path.exists(html_dir):
        app.mount("/static", StaticFiles(directory=html_dir), name="static")
        # 挂载assets目录用于Vue构建的静态资源
        assets_dir = os.path.join(html_dir, "assets")
        if os.path.exists(assets_dir):
            app.mount("/assets", StaticFiles(directory=assets_dir), name="assets")
            logger.info(f"✅ Assets静态文件服务已挂载: {assets_dir}")
        logger.info(f"✅ 静态文件服务已挂载: {html_dir}")
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        logger.info("🎉 FSY数据同步API服务已启动")
    
    @app.get("/")
    async def root():
        """根路径 - 重定向到数据看板"""
        html_file = os.path.join(html_dir, "index.html")
        if os.path.exists(html_file):
            return FileResponse(html_file)
        else:
            logger.info("访问根路径")
            return {"message": "FSY数据同步API服务正在运行", "dashboard": "/dashboard"}

    @app.get("/dashboard")
    async def dashboard():
        """数据看板页面"""
        html_file = os.path.join(html_dir, "index.html")
        if os.path.exists(html_file):
            return FileResponse(html_file)
        else:
            return {"error": "数据看板页面未找到"}

    @app.get("/changsha.json")
    async def get_changsha_map():
        """获取长沙地图数据"""
        map_file = os.path.join(html_dir, "changsha.json")
        if os.path.exists(map_file):
            return FileResponse(map_file, media_type="application/json")
        else:
            return {"error": "长沙地图数据未找到"}

    @app.get("/wuhan.json")
    async def get_wuhan_map():
        """获取武汉地图数据"""
        map_file = os.path.join(html_dir, "wuhan.json")
        if os.path.exists(map_file):
            return FileResponse(map_file, media_type="application/json")
        else:
            return {"error": "武汉地图数据未找到"}

    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}

    return app