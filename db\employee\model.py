"""
员工数据模型
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Index, BigInteger
from datetime import datetime
from typing import Dict, Any, Optional
from db.connection import Base


class Employee(Base):
    """员工上班数据表"""
    __tablename__ = 'employees'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    
    # 员工基本信息
    employee_id = Column(BigInteger, nullable=False, comment='员工ID')
    employee_code = Column(String(50), comment='员工编号')
    employee_name = Column(String(100), nullable=False, comment='员工姓名')
    
    # 店铺信息
    shop_id = Column(BigInteger, nullable=False, comment='店铺ID')
    shop_name = Column(String(200), comment='店铺名称')
    
    # 钟数统计
    additional_clock_num = Column(Float, default=0.0, comment='加钟数')
    call_clock_num = Column(Float, default=0.0, comment='叫钟数')
    extend_additional_clock_num = Column(Float, default=0.0, comment='延时加钟数')
    extend_call_clock_num = Column(Float, default=0.0, comment='延时叫钟数')
    extend_in_turn_clock_num = Column(Float, default=0.0, comment='延时轮钟数')
    extend_pointed_clock_num = Column(Float, default=0.0, comment='延时点钟数')
    in_turn_clock_num = Column(Float, default=0.0, comment='轮钟数')
    main_clock_num = Column(Integer, default=0, comment='主钟数')
    pointed_clock_num = Column(Float, default=0.0, comment='点钟数')
    small_clock_num = Column(Integer, default=0, comment='小钟数')
    total_clock_num = Column(Float, default=0.0, comment='总钟数')
    total_item_num = Column(Integer, default=0, comment='总项目数')
    
    # 时间信息
    date = Column(String(10), nullable=False, comment='数据日期，格式：YYYY-MM-DD')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 索引
    __table_args__ = (
        Index('idx_employee_id', 'employee_id'),
        Index('idx_shop_id', 'shop_id'),
        Index('idx_date', 'date'),
        Index('idx_employee_shop_date', 'employee_id', 'shop_id', 'date'),
    )
    
    @classmethod
    def from_api_data(cls, data: Dict[str, Any], date: str) -> 'Employee':
        """从API数据创建Employee实例"""
        return cls(
            employee_id=data.get('employeeId'),
            employee_code=data.get('employeeCode'),
            employee_name=data.get('employeeName'),
            shop_id=data.get('shopId'),
            shop_name=data.get('shopName'),
            additional_clock_num=data.get('additionalClockNum', 0.0),
            call_clock_num=data.get('callClockNum', 0.0),
            extend_additional_clock_num=data.get('extendAdditionalClockNum', 0.0),
            extend_call_clock_num=data.get('extendCallClockNum', 0.0),
            extend_in_turn_clock_num=data.get('extendInTurnClockNum', 0.0),
            extend_pointed_clock_num=data.get('extendPointedClockNum', 0.0),
            in_turn_clock_num=data.get('inTurnClockNum', 0.0),
            main_clock_num=data.get('mainClockNum', 0),
            pointed_clock_num=data.get('pointedClockNum', 0.0),
            small_clock_num=data.get('smallClockNum', 0),
            total_clock_num=data.get('totalClockNum', 0.0),
            total_item_num=data.get('totalItemNum', 0),
            date=date
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'employee_code': self.employee_code,
            'employee_name': self.employee_name,
            'shop_id': self.shop_id,
            'shop_name': self.shop_name,
            'additional_clock_num': self.additional_clock_num,
            'call_clock_num': self.call_clock_num,
            'extend_additional_clock_num': self.extend_additional_clock_num,
            'extend_call_clock_num': self.extend_call_clock_num,
            'extend_in_turn_clock_num': self.extend_in_turn_clock_num,
            'extend_pointed_clock_num': self.extend_pointed_clock_num,
            'in_turn_clock_num': self.in_turn_clock_num,
            'main_clock_num': self.main_clock_num,
            'pointed_clock_num': self.pointed_clock_num,
            'small_clock_num': self.small_clock_num,
            'total_clock_num': self.total_clock_num,
            'total_item_num': self.total_item_num,
            'date': self.date,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<Employee(id={self.id}, employee_id={self.employee_id}, name='{self.employee_name}', shop_id={self.shop_id}, total_clock_num={self.total_clock_num})>"