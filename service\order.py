# 订单服务层

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from crawler.index import get_shop_order_data, get_shop_commission_data
from db.order import order_dao, Order
from config import get_logger
from db.commission import commission_dao
import time

# 获取日志器
logger = get_logger(__name__)

def sync_shop_orders(shop_id: int, start_time: int, end_time: int, page_size: int = 50) -> Dict[str, Any]:
    """同步店铺订单数据
    
    Args:
        shop_id: 店铺ID
        start_time: 开始时间戳(毫秒)
        end_time: 结束时间戳(毫秒)
        page_size: 每页大小
        
    Returns:
        Dict: 同步结果统计
    """
    logger.info(f"开始同步店铺 {shop_id} 的订单数据...")
    
    total_added = 0
    total_updated = 0
    total_errors = 0
    page_no = 1
    
    try:
        # 初始化数据库表
        order_dao.db_manager.create_tables()
        
        while True:
            logger.info(f"正在获取第 {page_no} 页数据...")
            
            # 获取订单数据
            order_response = get_shop_order_data(shop_id, start_time, end_time, page_no, page_size)
            
            if not order_response or 'data' not in order_response:
                logger.info("没有更多订单数据")
                break
            
            orders_data = order_response['data']
            if not orders_data:
                logger.info("当前页没有订单数据")
                break
            
            logger.info(f"获取到 {len(orders_data)} 条订单数据")
            
            # 批量处理订单数据
            added, updated, errors = order_dao.batch_insert_or_update_orders(orders_data, shop_id)
            total_added += added
            total_updated += updated
            total_errors += errors
            
            # 检查是否还有更多数据
            total_record = order_response.get('totalRecord', 0)
            current_count = (page_no - 1) * page_size + len(orders_data)
            
            # 同步订单提成数据
            for order in orders_data:
                order_id = order.get('orderID')
                if not order_id:
                    logger.warning(f"订单数据缺少orderId字段: {order}")
                    continue
                    
                date = datetime.fromtimestamp(start_time / 1000).strftime('%Y-%m-%d')
                sync_shop_commission_data(order_id, shop_id, date)
                time.sleep(0.1)  # 添加延迟避免请求过于频繁
            
            if current_count >= total_record:
                logger.info("所有订单数据已同步完成")
                break
            
            page_no += 1
        
        
        return {
            'shop_id': shop_id,
            'added': total_added,
            'updated': total_updated,
            'errors': total_errors,
            'total_processed': total_added + total_updated + total_errors
        }
        
    except Exception as e:
        logger.error(f"同步订单数据时发生错误: {e}")
        return {
            'shop_id': shop_id,
            'added': total_added,
            'updated': total_updated,
            'errors': total_errors + 1,
            'error_message': str(e)
        }

# 同步订单提成数据
def sync_shop_commission_data(order_id: str, shop_id: str, date: str, max_retries: int = 3, retry_delay: float = 1.0) -> Dict[str, Any]:
    """同步订单提成数据
    
    Args:
        order_id: 订单ID
        shop_id: 店铺ID
        date: 日期
        max_retries: 最大重试次数
        retry_delay: 重试延迟时间(秒)
    """
    commission_data = None
    
    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            commission_data = get_shop_commission_data(order_id)
            break  # 成功获取数据，跳出重试循环
        except Exception as e:
            if attempt < max_retries:
                logger.warning(f"获取订单 {order_id} 提成数据失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}，{retry_delay}秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                logger.error(f"获取订单 {order_id} 提成数据最终失败: {e}")
                return {'inserted': 0, 'updated': 0, 'errors': 1}
    
    commissions_list = []
    
    if commission_data and commission_data['orderItemCommissions']:
        for item in commission_data['orderItemCommissions']:
            # 销售人员提成
            for sales in item['salesEmployeeCommissions']:
                commissions_list.append({
                    'employeeId': sales['employeeId'],
                    'orderItemId': item['orderItemId'],
                    'commission': sales['commission'],
                    'employeeName': sales['employeeName'],
                    'employ_type': 'sale',
                    'shop_id': shop_id,
                    'date': date
                })
            # 服务人员提成
            for service in item['serviceEmployeeCommissions']:
                commissions_list.append({
                    'employeeId': service['employeeId'],
                    'orderItemId': item['orderItemId'],
                    'commission': service['commission'],
                    'employeeName': service['employeeName'],
                    'employ_type': 'service',
                    'shop_id': shop_id,
                    'date': date
                })
    
    # 存储提成数据到数据库
    if commissions_list:
        result = commission_dao.batch_insert_or_update(commissions_list, order_id)
        logger.info(f"订单 {order_id} 提成数据同步完成: 插入 {result['inserted']} 条，更新 {result['updated']} 条，错误 {result['errors']} 条")
        return result
    else:
        logger.info(f"订单 {order_id} 没有提成数据")
        return {'inserted': 0, 'updated': 0, 'errors': 0}
    

def get_all_orders(limit: int = 50, offset: int = 0) -> List[Order]:
    """获取所有订单"""
    # 这里可以通过日期范围来获取所有订单
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)  # 获取一年内的订单
    orders = order_dao.get_orders_by_date_range(start_date, end_date, limit=limit, offset=offset)
    
    logger.info(f"获取到 {len(orders)} 个订单")

    return orders

def sync_all_order_commission():
    """同步所有订单的提成数据"""
    # 获取所有订单
    orders = get_all_orders(limit=20000)
    
    # 遍历每个订单并同步提成数据
    for order in orders:
        if order.status == 15:
            logger.info(f"跳过退款订单")
            continue
        logger.info(f"开始同步订单 {order.order_id} 的提成数据...")
        sync_shop_commission_data(order.order_id, order.shop_id, order.create_order_time.strftime('%Y-%m-%d'), max_retries=5, retry_delay=5.0)
        time.sleep(0.1)