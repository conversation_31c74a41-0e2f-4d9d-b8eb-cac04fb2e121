version: '3.8'

services:
  fsy-app:
    build: .
    container_name: fsy-app
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=talent@168
      - DB_NAME=fsy
    volumes:
      - ./logs:/app/logs
      - ./html:/app/html
    restart: unless-stopped
    depends_on:
      - mysql
    networks:
      - fsy-network

  mysql:
    container_name: mysql
    environment:
      TZ: 'Asia/Shanghai'
      MYSQL_ROOT_PASSWORD: "talent@168"
    image: "docker.ttata.com/mysql:5.7"
    restart: always
    ports:
      - "3306:3306"
    networks:
      - fsy-network
    volumes:
      - /data/docker/mysql/data:/var/lib/mysql
      - /data/docker/mysql/conf/my.cnf:/etc/mysql/conf.d/mysql.cnf

networks:
  fsy-network:
    driver: bridge
