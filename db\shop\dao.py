# 店铺数据库操作

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_
from typing import List, Optional
from datetime import datetime
from db.connection import db_manager
from db.shop.model import Shop
from config import get_logger

logger = get_logger(__name__)

class ShopDAO:
    """店铺数据访问对象 - SQLAlchemy版本"""
    
    def __init__(self):
        pass
    
    def init_table(self):
        """初始化表结构"""
        return db_manager.create_tables()
    
    def batch_insert_or_update(self, shop_records: List[dict]) -> bool:
        """批量插入或更新店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                for record in shop_records:
                    # 检查是否已存在相同的店铺（根据org_node_id）
                    existing_shop = session.query(Shop).filter(
                        Shop.org_node_id == record['org_node_id']
                    ).first()
                    
                    if existing_shop:
                        # 更新现有店铺
                        for key, value in record.items():
                            if hasattr(existing_shop, key):
                                setattr(existing_shop, key, value)
                        existing_shop.updated_at = datetime.now()
                    else:
                        # 创建新店铺
                        shop = Shop(**record)
                        session.add(shop)
                
                logger.info(f"✅ 成功同步 {len(shop_records)} 个店铺数据到数据库")
                return True
                
            except SQLAlchemyError as e:
                logger.error(f"❌ 批量插入店铺数据失败: {e}")
                return False
    
    def get_all_shops(self) -> List[Shop]:
        """获取所有店铺数据"""
        with db_manager.get_session_context() as session:
            try:
                shops = session.query(Shop).order_by(Shop.created_at.desc()).all()
                
                # 确保所有属性都已加载
                for shop in shops:
                    # 访问所有可能需要的属性以确保它们已加载
                    _ = shop.id
                    _ = shop.org_node_id
                    _ = shop.name
                    _ = shop.type
                    _ = shop.shop_id
                    _ = shop.chain_type
                    _ = shop.mt_shop_id
                    _ = shop.status
                    _ = shop.lng
                    _ = shop.lat
                    _ = shop.created_at
                    _ = shop.updated_at
                
                # 将对象从Session中分离，使其可以在Session外使用
                session.expunge_all()
                
                return shops
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺数据失败: {e}")
                return []
    
    def get_shop_by_org_node_id(self, org_node_id: int) -> Optional[Shop]:
        """根据org_node_id获取店铺"""
        with db_manager.get_session_context() as session:
            try:
                shop = session.query(Shop).filter(Shop.org_node_id == org_node_id).first()
                
                if shop:
                    # 确保所有属性都已加载
                    _ = shop.id
                    _ = shop.org_node_id
                    _ = shop.name
                    _ = shop.type
                    _ = shop.shop_id
                    _ = shop.chain_type
                    _ = shop.mt_shop_id
                    _ = shop.status
                    _ = shop.lng
                    _ = shop.lat
                    _ = shop.created_at
                    _ = shop.updated_at
                    
                    # 将对象从Session中分离
                    session.expunge(shop)
                
                return shop
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺数据失败: {e}")
                return None
    
    def get_shop_by_id(self, shop_id: int) -> Optional[Shop]:
        """根据主键ID获取店铺"""
        with db_manager.get_session_context() as session:
            try:
                shop = session.query(Shop).filter(Shop.shop_id == shop_id).first()
                
                if shop:
                    # 确保所有属性都已加载
                    _ = shop.id
                    _ = shop.org_node_id
                    _ = shop.name
                    _ = shop.type
                    _ = shop.shop_id
                    _ = shop.chain_type
                    _ = shop.mt_shop_id
                    _ = shop.status
                    _ = shop.lng
                    _ = shop.lat
                    _ = shop.created_at
                    _ = shop.updated_at
                    
                    # 将对象从Session中分离
                    session.expunge(shop)
                
                return shop
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺数据失败: {e}")
                return None

    def get_shops_by_status(self, status: int) -> List[Shop]:
        """根据状态获取店铺列表"""
        with db_manager.get_session_context() as session:
            try:
                shops = session.query(Shop).filter(Shop.status == status).order_by(Shop.created_at.desc()).all()
                
                # 确保所有属性都已加载
                for shop in shops:
                    # 访问所有可能需要的属性以确保它们已加载
                    _ = shop.id
                    _ = shop.org_node_id
                    _ = shop.name
                    _ = shop.type
                    _ = shop.shop_id
                    _ = shop.chain_type
                    _ = shop.mt_shop_id
                    _ = shop.status
                    _ = shop.lng
                    _ = shop.lat
                    _ = shop.created_at
                    _ = shop.updated_at
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return shops
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺数据失败: {e}")
                return []

    def search_shops(self, keyword: str) -> List[Shop]:
        """搜索店铺（按名称）"""
        with db_manager.get_session_context() as session:
            try:
                shops = session.query(Shop).filter(
                    Shop.name.like(f'%{keyword}%')
                ).order_by(Shop.created_at.desc()).all()
                
                # 确保所有属性都已加载
                for shop in shops:
                    # 访问所有可能需要的属性以确保它们已加载
                    _ = shop.id
                    _ = shop.org_node_id
                    _ = shop.name
                    _ = shop.type
                    _ = shop.shop_id
                    _ = shop.chain_type
                    _ = shop.mt_shop_id
                    _ = shop.status
                    _ = shop.lng
                    _ = shop.lat
                    _ = shop.created_at
                    _ = shop.updated_at
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return shops
            except SQLAlchemyError as e:
                logger.error(f"❌ 搜索店铺数据失败: {e}")
                return []

    def delete_shop(self, shop_id: int) -> bool:
        """删除店铺"""
        with db_manager.get_session_context() as session:
            try:
                shop = session.query(Shop).filter(Shop.id == shop_id).first()
                if shop:
                    session.delete(shop)
                    logger.info(f"✅ 成功删除店铺: {shop.name}")
                    return True
                else:
                    logger.warning(f"⚠️ 未找到ID为 {shop_id} 的店铺")
                    return False
            except SQLAlchemyError as e:
                logger.error(f"❌ 删除店铺失败: {e}")
                return False

    def update_shop_status(self, shop_id: int, status: int) -> bool:
        """更新店铺状态"""
        with db_manager.get_session_context() as session:
            try:
                shop = session.query(Shop).filter(Shop.id == shop_id).first()
                if shop:
                    shop.status = status
                    shop.updated_at = datetime.now()
                    logger.info(f"✅ 成功更新店铺状态: {shop.name} -> {status}")
                    return True
                else:
                    logger.warning(f"⚠️ 未找到ID为 {shop_id} 的店铺")
                    return False
            except SQLAlchemyError as e:
                logger.error(f"❌ 更新店铺状态失败: {e}")
                return False

    def get_shop_count(self) -> int:
        """获取店铺总数"""
        with db_manager.get_session_context() as session:
            try:
                return session.query(Shop).count()
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺总数失败: {e}")
                return 0

# 全局店铺DAO实例
shop_dao = ShopDAO()