# 订单数据访问对象

from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, asc, func
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from db.connection import db_manager
from .model import Order, OrderProduct, OrderPayment, OrderPromo, OrderArtist
from config import get_logger

logger = get_logger(__name__)

class OrderDAO:
    """订单数据访问对象"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def batch_insert_or_update_orders(self, orders_data: List[Dict[str, Any]], shop_id: int) -> Tuple[int, int, int]:
        """批量插入或更新订单数据
        
        Args:
            orders_data: 订单数据列表
            
        Returns:
            Tuple[int, int, int]: (新增数量, 更新数量, 错误数量)
        """
        with self.db_manager.get_session_context() as session:
            added_count = 0
            updated_count = 0
            error_count = 0
            for order_data in orders_data:
                # 接口返回的shop_id为空，手动赋值
                order_data['shopID'] = shop_id
                try:
                    # 检查订单是否已存在
                    existing_order = session.query(Order).filter(
                        Order.order_id == order_data.get('orderID')
                    ).first()
                    
                    if existing_order:
                        # 更新现有订单
                        self._update_order_from_data(existing_order, order_data)
                        updated_count += 1
                    else:
                        # 创建新订单
                        new_order = Order.from_api_data(order_data)
                        session.add(new_order)
                        session.flush()  # 获取订单ID
                        
                        # 处理关联数据
                        self._process_order_relations(session, new_order, order_data)
                        added_count += 1
                        
                except Exception as e:
                    logger.error(f"处理订单 {order_data.get('orderID', 'Unknown')} 时出错: {e}")
                    error_count += 1
                    continue
            
            logger.info(f"订单数据同步完成: 新增 {added_count}, 更新 {updated_count}, 错误 {error_count}")
            
            return added_count, updated_count, error_count
    
    def _update_order_from_data(self, order: Order, data: Dict[str, Any]):
        """从API数据更新订单"""
        # 更新基本字段
        order.order_display_id = data.get('orderDisplayID', order.order_display_id)
        order.status = data.get('status', order.status)
        order.amount = data.get('amount', order.amount)
        order.payed_amount = data.get('payedAmount', order.payed_amount)
        order.pay_amount = data.get('payAmount', order.pay_amount)
        
        # 更新时间字段
        if data.get('payTime') and not order.pay_time:
            order.pay_time = datetime.strptime(data['payTime'], '%Y-%m-%d %H:%M:%S')
        
        if data.get('refundTime') and not order.refund_time:
            order.refund_time = datetime.strptime(data['refundTime'], '%Y-%m-%d %H:%M:%S')
        
        # 更新最后操作日志
        last_op = data.get('lastOperationLog', {})
        if last_op.get('operationTime'):
            last_op_time = datetime.strptime(last_op['operationTime'], '%Y-%m-%d %H:%M:%S')
            if not order.last_operation_time or last_op_time > order.last_operation_time:
                order.last_operation_employee_id = last_op.get('operationEmployeeID')
                order.last_operation_employee_name = last_op.get('operationEmployeeName')
                order.last_operation_time = last_op_time
                order.last_operation_type = last_op.get('operationType')
                order.last_operation_type_name = last_op.get('operationTypeName')
    
    def _process_order_relations(self, session: Session, order: Order, data: Dict[str, Any]):
        """处理订单关联数据"""
        # 处理商品数据 - 确保products不为None
        products_data = data.get('products')
        if products_data is None:
            products_data = []
        
        for product_data in products_data:
            
            quantity = product_data.get('quantity')
            if quantity is None:
                quantity = 1.0  # 默认数量为1
            
            quantity2 = product_data.get('quantity2')
            if quantity2 is None:
                quantity2 = 1.0  # 默认数量为1
            
            order_sku_id = product_data.get('orderSkuID')
            if order_sku_id is None:
                order_sku_id = 0  # 默认为0
            
            sku_id = product_data.get('skuID')
            if sku_id is None:
                sku_id = 0  # 默认为0
            
            sku_name = product_data.get('skuName')
            if sku_name is None:
                sku_name = '未知商品'  # 默认商品名
            
            sku_type = product_data.get('skuType')
            if sku_type is None:
                sku_type = 1  # 默认类型为1
            
            order_product = OrderProduct(
                order_id=order.order_id,
                order_sku_id=order_sku_id,
                sku_id=sku_id,
                sku_name=sku_name,
                sku_type=sku_type,
                quantity=quantity,
                quantity2=quantity2,
                deal_id=product_data.get('dealID'),
                extra_free_amount=product_data.get('extraFreeAmount'),
                goupon_code=product_data.get('gouponCode'),
                is_add_clock_item=product_data.get('isAddClockItem', 0),
                total_promo_amount=product_data.get('totalPromoAmount'),
                stored_value_pay_amount=product_data.get('storedValuePayAmount'),
                to_sku_name=product_data.get('toSkuName'),
                total_times=product_data.get('totalTimes')
            )
            session.add(order_product)
            
            # 处理促销数据 - 确保promos不为None
            promos_data = product_data.get('promos')
            if promos_data is None:
                promos_data = []
                
            for promo_data in promos_data:
                # 确保必需字段有默认值
                promo_id = promo_data.get('promoID')
                if promo_id is None:
                    continue  # 跳过没有促销ID的记录
                
                promo_type = promo_data.get('promoType')
                if promo_type is None:
                    promo_type = 0  # 默认促销类型
                
                amount = promo_data.get('amount')
                if amount is None:
                    amount = 0.0  # 默认金额为0
                
                order_promo = OrderPromo(
                    order_id=order.order_id,
                    order_sku_id=order_sku_id,
                    promo_id=promo_id,
                    promo_type=promo_type,
                    amount=amount,
                    discount_id=promo_data.get('discountId'),
                    discount=promo_data.get('discount'),
                    description=promo_data.get('description'),
                    card_no=promo_data.get('cardNO'),
                    coupon_name=promo_data.get('couponName')
                )
                session.add(order_promo)
            
            # 处理销售人员 - 确保sales不为None
            sales_data = product_data.get('sales')
            if sales_data is None:
                sales_data = []
                
            for sale_data in sales_data:
                # 确保必需字段有默认值
                sale_id = sale_data.get('saleID')
                if sale_id is None:
                    continue  # 跳过没有销售ID的记录
                
                sale_name = sale_data.get('saleName')
                if sale_name is None:
                    sale_name = '未知销售'  # 默认销售名
                
                order_artist = OrderArtist(
                    order_id=order.order_id,
                    order_sku_id=order_sku_id,
                    artist_type='sales',
                    artist_id=sale_id,
                    artist_name=sale_name,
                    artist_code=sale_data.get('saleCode'),
                    is_artist_appoint=0
                )
                session.add(order_artist)
            
            # 处理技师 - 确保artists不为None
            artists_data = product_data.get('artists')
            if artists_data is None:
                artists_data = []
                
            for artist_data in artists_data:
                # 确保必需字段有默认值
                artist_id = artist_data.get('artistID')
                if artist_id is None:
                    continue  # 跳过没有技师ID的记录
                
                artist_name = artist_data.get('artistName')
                if artist_name is None:
                    artist_name = '未知技师'  # 默认技师名
                
                order_artist = OrderArtist(
                    order_id=order.order_id,
                    order_sku_id=order_sku_id,
                    artist_type='artist',
                    artist_id=artist_id,
                    artist_name=artist_name,
                    artist_code=artist_data.get('artistCode'),
                    is_artist_appoint=artist_data.get('isArtistAppoint', 0),
                    artist_work_mode=artist_data.get('artistWorkMode'),
                    add_clock=artist_data.get('addClock'),
                    add_time=artist_data.get('addTime'),
                    assist_artist_id=artist_data.get('assistArtistID'),
                    assist_artist_name=artist_data.get('assistArtistName'),
                    assist_artist_code=artist_data.get('assistArtistCode'),
                    is_assist_artist_appoint=artist_data.get('isAssistArtistAppoint'),
                    assist_artist_work_mode=artist_data.get('assistArtistWorkMode')
                )
                session.add(order_artist)
        
        # 处理支付数据 - 确保paymentDetailDTOS不为None
        payments_data = data.get('paymentDetailDTOS')
        if payments_data is None:
            payments_data = []
            
        for payment_data in payments_data:
            # 确保必需字段有默认值
            amount_type = payment_data.get('amountType')
            if amount_type is None:
                continue  # 跳过没有金额类型的记录
            
            name = payment_data.get('name')
            if name is None:
                name = '未知支付方式'  # 默认支付方式名
            
            pay_amount = payment_data.get('payAmount')
            if pay_amount is None:
                pay_amount = 0.0  # 默认支付金额为0
            
            pay_status = payment_data.get('payStatus')
            if pay_status is None:
                pay_status = 0  # 默认支付状态
            
            order_payment = OrderPayment(
                order_id=order.order_id,
                amount_type=amount_type,
                name=name,
                pay_amount=pay_amount,
                pay_status=pay_status
            )
            session.add(order_payment)
    
    def get_order_by_id(self, order_id: int) -> Optional[Order]:
        """根据订单ID获取订单"""
        with self.db_manager.get_session_context() as session:
            order = session.query(Order).options(
                joinedload(Order.artists),
                joinedload(Order.products),
                joinedload(Order.payments)
            ).filter(Order.order_id == order_id).first()
            
            if order:
                # 触发关联数据的加载
                _ = len(order.artists)
                _ = len(order.products)
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
                
                # 将对象从Session中分离
                session.expunge(order)
            
            return order
    
    def get_order_by_display_id(self, display_id: str) -> Optional[Order]:
        """根据订单显示ID获取订单"""
        with self.db_manager.get_session_context() as session:
            order = session.query(Order).options(
                joinedload(Order.artists),
                joinedload(Order.products),
                joinedload(Order.payments)
            ).filter(Order.order_display_id == display_id).first()
            
            if order:
                # 触发关联数据的加载
                _ = len(order.artists)
                _ = len(order.products)
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
                
                # 将对象从Session中分离
                session.expunge(order)
            
            return order
    
    def get_orders_by_shop(self, shop_id: int, limit: int = 100, offset: int = 0) -> List[Order]:
        """根据店铺ID获取订单列表"""
        with self.db_manager.get_session_context() as session:
            orders = session.query(Order).options(
                joinedload(Order.artists),
                joinedload(Order.products),
                joinedload(Order.payments)
            ).filter(
                Order.shop_id == shop_id
            ).order_by(desc(Order.create_order_time)).limit(limit).offset(offset).all()
            
            # 确保关联数据已加载
            for order in orders:
                _ = len(order.artists)
                _ = len(order.products)
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
            
            # 将对象从Session中分离
            session.expunge_all()
            
            return orders
    
    def get_orders_by_date_range(self, start_date: datetime, end_date: datetime, 
                                shop_id: Optional[int] = None, limit: int = 100, offset: int = 0) -> List[Order]:
        """根据日期范围获取订单（包含artists关联数据）"""
        with self.db_manager.get_session_context() as session:
            query = session.query(Order).options(
                joinedload(Order.artists),  # 预加载artists关联数据
                joinedload(Order.products),  # 预加载products关联数据
                joinedload(Order.payments)   # 预加载payments关联数据
            ).filter(
                and_(
                    Order.create_order_time >= start_date,
                    Order.create_order_time <= end_date
                )
            )
            
            if shop_id:
                query = query.filter(Order.shop_id == shop_id)
            
            orders = query.order_by(desc(Order.create_order_time)).limit(limit).offset(offset).all()
            
            # 确保所有关联数据都已加载到内存中
            for order in orders:
                # 触发关联数据的加载
                _ = len(order.artists)
                _ = len(order.products) 
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
            
            # 将对象从Session中分离，使其可以在Session外使用
            session.expunge_all()
            
            return orders
    
    def get_orders_by_status(self, status: int, limit: int = 100, offset: int = 0) -> List[Order]:
        """根据状态获取订单"""
        with self.db_manager.get_session_context() as session:
            orders = session.query(Order).options(
                joinedload(Order.artists),
                joinedload(Order.products),
                joinedload(Order.payments)
            ).filter(
                Order.status == status
            ).order_by(desc(Order.create_order_time)).limit(limit).offset(offset).all()
            
            # 确保关联数据已加载
            for order in orders:
                _ = len(order.artists)
                _ = len(order.products)
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
            
            # 将对象从Session中分离
            session.expunge_all()
            
            return orders
    
    def search_orders_by_customer(self, keyword: str, limit: int = 100, offset: int = 0) -> List[Order]:
        """根据客户信息搜索订单"""
        with self.db_manager.get_session_context() as session:
            orders = session.query(Order).options(
                joinedload(Order.artists),
                joinedload(Order.products),
                joinedload(Order.payments)
            ).filter(
                or_(
                    Order.customer_name.like(f'%{keyword}%'),
                    Order.mobile_no.like(f'%{keyword}%'),
                    Order.membership_name.like(f'%{keyword}%')
                )
            ).order_by(desc(Order.create_order_time)).limit(limit).offset(offset).all()
            
            # 确保关联数据已加载
            for order in orders:
                _ = len(order.artists)
                _ = len(order.products)
                _ = len(order.payments)
                
                # 访问关联对象的属性以确保完全加载
                for artist in order.artists:
                    _ = artist.artist_name
                for product in order.products:
                    _ = product.sku_name
                for payment in order.payments:
                    _ = payment.name
            
            # 将对象从Session中分离
            session.expunge_all()
            
            return orders
    
    def get_order_statistics(self, shop_id: Optional[int] = None, 
                           start_date: Optional[datetime] = None, 
                           end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取订单统计信息"""
        with self.db_manager.get_session_context() as session:
            query = session.query(Order)
            
            # 添加筛选条件
            if shop_id:
                query = query.filter(Order.shop_id == shop_id)
            if start_date:
                query = query.filter(Order.create_order_time >= start_date)
            if end_date:
                query = query.filter(Order.create_order_time <= end_date)
            
            # 统计数据
            total_orders = query.count()
            total_amount = query.with_entities(func.sum(Order.amount)).scalar() or 0
            total_pay_amount = query.with_entities(func.sum(Order.pay_amount)).scalar() or 0
            avg_amount = query.with_entities(func.avg(Order.amount)).scalar() or 0
            
            # 按状态统计
            status_stats = session.query(
                Order.status, 
                func.count(Order.id).label('count'),
                func.sum(Order.amount).label('amount')
            ).filter(query.whereclause if query.whereclause is not None else True).group_by(Order.status).all()
            
            return {
                'total_orders': total_orders,
                'total_amount': float(total_amount),
                'total_pay_amount': float(total_pay_amount),
                'avg_amount': float(avg_amount),
                'status_statistics': [
                    {
                        'status': stat.status,
                        'count': stat.count,
                        'amount': float(stat.amount or 0)
                    } for stat in status_stats
                ]
            }
    
    def get_order_count(self) -> int:
        """获取订单总数"""
        with self.db_manager.get_session_context() as session:
            return session.query(Order).count()
    
    def delete_order_by_id(self, order_id: int) -> bool:
        """根据订单ID删除订单"""
        with self.db_manager.get_session_context() as session:
            try:
                order = session.query(Order).filter(Order.order_id == order_id).first()
                if order:
                    session.delete(order)
                    return True
                return False
            except SQLAlchemyError as e:
                logger.error(f"删除订单失败: {e}")
                return False

# 全局订单DAO实例
order_dao = OrderDAO()