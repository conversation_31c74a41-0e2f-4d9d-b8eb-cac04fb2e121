# 数据库连接管理

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError
from urllib.parse import quote_plus
from config import DATABASE_CONFIG, get_logger

logger = get_logger(__name__)

# 创建基础模型类
Base = declarative_base()

class DatabaseManager:
    """SQLAlchemy数据库连接管理器"""
    
    def __init__(self):
        self.config = DATABASE_CONFIG
        self.engine = None
        self.SessionLocal = None
        self._init_engine()
    
    def _init_engine(self):
        """初始化数据库引擎"""
        try:
            # 构建数据库连接URL，对密码进行URL编码以处理特殊字符
            encoded_password = quote_plus(self.config['password'])
            db_url = f"mysql+pymysql://{self.config['user']}:{encoded_password}@{self.config['host']}:{self.config['port']}/{self.config['database']}?charset={self.config['charset']}"
            
            # 创建引擎，配置连接池
            self.engine = create_engine(
                db_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,  # 连接池预检查
                pool_recycle=3600,   # 连接回收时间（1小时）
                pool_size=10,        # 连接池大小
                max_overflow=20,     # 最大溢出连接数
                pool_timeout=30,     # 获取连接超时时间
                connect_args={
                    'connect_timeout': 60,
                    'read_timeout': 60,
                    'write_timeout': 60,
                }
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            logger.info("SQLAlchemy引擎和连接池初始化成功")
            
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {e}")
            raise
    
    def create_database(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            temp_url = f"mysql+pymysql://{self.config['user']}:{self.config['password']}@{self.config['host']}:{self.config['port']}?charset={self.config['charset']}"
            temp_engine = create_engine(temp_url)
            
            with temp_engine.connect() as connection:
                # 创建数据库
                connection.execute(text(f"CREATE DATABASE IF NOT EXISTS {self.config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                connection.commit()
            
            temp_engine.dispose()
            logger.info(f"数据库 {self.config['database']} 创建成功")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"创建数据库失败: {e}")
            return False
    
    def create_tables(self):
        """创建所有表"""
        try:
            # 先确保数据库存在
            self.create_database()
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"创建表失败: {e}")
            return False
    
    def get_session(self):
        """获取数据库会话"""
        if not self.SessionLocal:
            raise RuntimeError("数据库未初始化")
        return self.SessionLocal()
    
    def get_engine(self):
        """获取数据库引擎"""
        return self.engine
    
    def get_session_context(self):
        """获取Session上下文管理器"""
        return SessionContext(self.SessionLocal)

class SessionContext:
    """Session上下文管理器"""
    
    def __init__(self, session_factory):
        self.session_factory = session_factory
        self.session = None
    
    def __enter__(self):
        self.session = self.session_factory()
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.session.rollback()
        else:
            self.session.commit()
        self.session.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()