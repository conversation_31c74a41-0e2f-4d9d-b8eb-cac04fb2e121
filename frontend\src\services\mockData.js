// 模拟数据服务
export const mockData = {
  // 模拟汇总数据
  getAnalysisSummary() {
    return {
      metrics: {
        totalRevenue: { value: 2850000, change: 12.5 },
        totalCashFlow: { value: 1950000, change: 8.3 },
        totalTraffic: { value: 15420, change: 15.2 },
        totalCardConsumption: { value: 850000, change: -2.1 },
        totalMeituan: { value: 420000, change: 18.7 },
        totalDouyin: { value: 380000, change: 25.4 },
        totalOnline: { value: 1200000, change: 22.1 },
        totalOffline: { value: 1650000, change: 5.8 },
        totalCard: { value: 850000, change: -2.1 },
        totalReviews: { value: 4.8, change: 0.2 }
      }
    }
  },

  // 模拟排行榜数据
  getRankings(type = 'revenue') {
    const rankings = {
      revenue: [
        { name: '长沙岳麓店', value: 485000, change: 15.2 },
        { name: '武汉江汉店', value: 425000, change: 12.8 },
        { name: '长沙芙蓉店', value: 398000, change: 8.5 },
        { name: '武汉武昌店', value: 365000, change: 18.3 },
        { name: '长沙天心店', value: 342000, change: 5.7 },
        { name: '长沙开福店', value: 315000, change: -2.1 },
        { name: '长沙雨花店', value: 285000, change: 9.4 }
      ],
      traffic: [
        { name: '长沙岳麓店', value: 2850, change: 18.5 },
        { name: '武汉江汉店', value: 2420, change: 15.2 },
        { name: '长沙芙蓉店', value: 2180, change: 12.3 },
        { name: '武汉武昌店', value: 1950, change: 22.1 },
        { name: '长沙天心店', value: 1820, change: 8.7 },
        { name: '长沙开福店', value: 1650, change: -1.5 },
        { name: '长沙雨花店', value: 1485, change: 11.2 }
      ],
      reviews: [
        { name: '长沙岳麓店', value: 4.9, change: 0.1 },
        { name: '武汉江汉店', value: 4.8, change: 0.2 },
        { name: '长沙芙蓉店', value: 4.8, change: 0.0 },
        { name: '武汉武昌店', value: 4.7, change: 0.3 },
        { name: '长沙天心店', value: 4.7, change: -0.1 },
        { name: '长沙开福店', value: 4.6, change: 0.1 },
        { name: '长沙雨花店', value: 4.5, change: 0.2 }
      ]
    }
    return { rankings: rankings[type] || rankings.revenue }
  },

  // 模拟趋势数据
  getTrends(type = 'revenue', days = 7) {
    const baseData = {
      revenue: [180000, 195000, 210000, 185000, 225000, 240000, 285000],
      traffic: [1200, 1350, 1480, 1320, 1580, 1650, 1820],
      reviews: [4.5, 4.6, 4.7, 4.6, 4.8, 4.8, 4.9]
    }
    
    const dates = []
    const today = new Date()
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
    }
    
    return {
      dates,
      values: baseData[type] || baseData.revenue,
      type
    }
  },

  // 模拟门店数据
  getStores() {
    return [
      { id: "all", name: "全部门店", value: "all" },
      { id: "1", name: "长沙岳麓店", value: "1" },
      { id: "2", name: "长沙芙蓉店", value: "2" },
      { id: "3", name: "长沙天心店", value: "3" },
      { id: "4", name: "长沙开福店", value: "4" },
      { id: "5", name: "长沙雨花店", value: "5" },
      { id: "6", name: "武汉江汉店", value: "6" },
      { id: "7", name: "武汉武昌店", value: "7" }
    ]
  },

  // 模拟地图数据
  getMapData() {
    return {
      stores: [
        {
          name: '长沙岳麓店',
          coord: [112.938, 28.235],
          value: 485000,
          traffic: 2850,
          reviews: 4.9
        },
        {
          name: '长沙芙蓉店',
          coord: [113.025, 28.198],
          value: 398000,
          traffic: 2180,
          reviews: 4.8
        },
        {
          name: '长沙天心店',
          coord: [112.989, 28.112],
          value: 342000,
          traffic: 1820,
          reviews: 4.7
        },
        {
          name: '长沙开福店',
          coord: [112.985, 28.256],
          value: 315000,
          traffic: 1650,
          reviews: 4.6
        },
        {
          name: '长沙雨花店',
          coord: [113.065, 28.135],
          value: 285000,
          traffic: 1485,
          reviews: 4.5
        },
        {
          name: '武汉江汉店',
          coord: [114.265, 30.584],
          value: 425000,
          traffic: 2420,
          reviews: 4.8
        },
        {
          name: '武汉武昌店',
          coord: [114.315, 30.542],
          value: 365000,
          traffic: 1950,
          reviews: 4.7
        }
      ],
      heatmapData: [
        { name: '长沙市', value: [112.982, 28.19, 1820000] },
        { name: '武汉市', value: [114.29, 30.563, 790000] }
      ]
    }
  }
}

export default mockData