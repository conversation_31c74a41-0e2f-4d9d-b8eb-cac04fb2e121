from fastapi import APIRouter, HTTPException, Query
from config import get_logger
from service.shop import sync_data

logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/sync", tags=["数据同步"])

@router.post("/data")
async def sync_data_with_date(date: str = Query(..., description="数据同步日期，格式YYYY-MM-DD")):
    """
    同步指定日期的数据
    """
    try:
        sync_data(date)
        return {"message": f"数据同步成功，日期: {date}"}
    except Exception as e:
        logger.error(f"数据同步失败，日期: {date}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail="数据同步失败")

