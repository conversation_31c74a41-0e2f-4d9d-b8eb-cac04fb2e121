# 方松院店铺数据爬虫

这个项目用于爬取方松院店铺数据并存储到MySQL数据库中。

## 🌟 功能特性

- 🕷️ 爬取美团店铺组织架构数据
- 💾 自动存储到MySQL数据库
- 🔄 支持数据更新（避免重复插入）
- 📊 显示同步结果统计
- 🏗️ 清晰的分层架构设计
- 🔍 支持店铺查询和删除操作

## 📦 安装依赖

使用 uv 安装项目依赖：

```bash
uv sync
```

## ⚙️ 配置数据库

1. 确保MySQL服务已启动
2. 修改 `config.py` 中的数据库配置：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_mysql_password',  # 修改为你的MySQL密码
    'database': 'fsy_shops',
    'charset': 'utf8mb4'
}
```

## 🚀 运行程序

```bash
uv run main.py
```

## 🗄️ 数据库表结构

程序会自动创建数据库和表，表结构如下：

```sql
CREATE TABLE shops (
    id INT AUTO_INCREMENT PRIMARY KEY,
    org_node_id INT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL,
    shop_id INT,
    chain_type TINYINT,
    status TINYINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 📁 项目结构

```
fsy/
├── crawler/                  # 爬虫模块
│   └── index.py             # 爬虫实现
├── service/                 # 服务层
│   └── sync.py              # 业务逻辑处理
├── db/                      # 数据库层
│   ├── connection.py        # 数据库连接管理
│   ├── shop/                # 店铺相关数据库操作
│   │   ├── __init__.py      # 模块导出
│   │   ├── dao.py           # 数据访问对象
│   │   └── model.py         # 数据模型
│   └── init.py              # 数据库模块初始化
├── config.py                # 配置文件
├── main.py                  # 主程序入口
└── README.md                # 项目说明
```

## 🏗️ 架构设计

### 分层架构
- **爬虫层 (crawler/)**: 负责数据获取
- **服务层 (service/)**: 负责业务逻辑处理
- **数据库层 (db/)**: 负责数据持久化

### 核心组件
- **DatabaseManager**: 数据库连接管理
- **ShopDAO**: 店铺数据访问对象
- **Shop**: 店铺数据模型
- **sync_shop()**: 主要业务逻辑

## 🔧 API 说明

### 服务层 API

```python
from service.sync import sync_shop, get_shop_by_id, delete_shop_by_id

# 同步店铺数据
success = sync_shop()

# 查询店铺
shop = get_shop_by_id(84319)

# 删除店铺
success = delete_shop_by_id(84319)
```

### 数据库层 API

```python
from db.shop import shop_dao, Shop

# 初始化表
shop_dao.init_table()

# 批量插入
shop_dao.batch_insert_or_update(records)

# 查询所有店铺
shops = shop_dao.get_all_shops()

# 根据ID查询
shop = shop_dao.get_shop_by_org_node_id(84319)
```

## ⚠️ 注意事项

- 请确保MySQL服务正在运行
- 修改配置文件中的数据库密码
- 程序会自动创建数据库和表
- 支持重复运行，会自动更新已存在的数据
- 数据库操作已与业务逻辑分离，便于维护和扩展