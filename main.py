"""
主程序入口
"""
from re import T
from db.connection import db_manager
from config import setup_logging, get_logger
import uvicorn
import asyncio
import signal
import sys

logger = get_logger(__name__)

# 全局变量用于存储调度器实例
scheduler_instance = None


def init_database():
    """初始化数据库"""
    logger.info("📊 初始化数据库...")
    if db_manager.create_tables():
        logger.info("✅ 数据库表创建成功")
        return True
    else:
        logger.error("❌ 数据库表创建失败")
        return False


def init_scheduler():
    """初始化定时任务调度器"""
    global scheduler_instance
    try:
        logger.info("🕐 初始化定时任务调度器...")
        from scheduler.sync_scheduler import data_sync_scheduler
        scheduler_instance = data_sync_scheduler
        scheduler_instance.start()
        logger.info("✅ 定时任务调度器启动成功")
        return True
    except Exception as e:
        logger.error(f"❌ 定时任务调度器启动失败: {str(e)}")
        return False


def stop_scheduler():
    """停止定时任务调度器"""
    global scheduler_instance
    if scheduler_instance:
        try:
            scheduler_instance.stop()
            logger.info("🛑 定时任务调度器已停止")
        except Exception as e:
            logger.error(f"停止定时任务调度器失败: {str(e)}")


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在优雅关闭...")
    stop_scheduler()
    sys.exit(0)


def run_api_server():
    """运行API服务器"""
    
    logger.info("🚀 启动API服务器...")
    # 配置uvicorn使用我们的日志配置
    uvicorn.run(
        "api:create_app", 
        host="0.0.0.0", 
        port=8000, 
        log_level="info", 
        reload=False, 
        factory=True,
        access_log=True,
        use_colors=True
    )

def main():
    # 首先初始化日志配置
    setup_logging()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 初始化数据库
    if not init_database():
        logger.error("数据库初始化失败，程序退出")
        sys.exit(1)
    
    # 初始化定时任务调度器
    if not init_scheduler():
        logger.warning("定时任务调度器初始化失败，但程序继续运行")
    
    try:
        # 启动API服务器
        run_api_server()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(f"API服务器运行异常: {str(e)}")
    finally:
        # 确保调度器被正确停止
        stop_scheduler()

if __name__ == "__main__":
    main()
