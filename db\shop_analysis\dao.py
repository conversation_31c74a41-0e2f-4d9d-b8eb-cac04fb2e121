# 店铺分析数据DAO

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_
from db.connection import db_manager
from db.shop_analysis.model import ShopAnalysis
from config import get_logger

logger = get_logger(__name__)

class ShopAnalysisDAO:
    """店铺分析数据访问对象"""
    
    def __init__(self):
        """初始化DAO"""
        self.model = ShopAnalysis
    
    def init_table(self) -> bool:
        """初始化数据表"""
        try:
            with db_manager.get_engine() as engine:
                ShopAnalysis.metadata.create_all(engine)
                logger.info("✅ 店铺分析数据表初始化成功")
                return True
        except Exception as e:
            logger.error(f"❌ 店铺分析数据表初始化失败: {e}")
            return False
    
    def save_analysis_data(self, analysis_data: Dict[str, Any]) -> bool:
        """保存或更新店铺分析数据
        
        Args:
            analysis_data: 分析数据字典
            
        Returns:
            bool: 保存是否成功
        """
        with db_manager.get_session_context() as session:
            try:
                # 检查是否已存在该店铺该日期的数据
                existing = session.query(ShopAnalysis).filter(
                    and_(
                        ShopAnalysis.shop_id == analysis_data['shop_id'],
                        ShopAnalysis.analysis_date == analysis_data['analysis_date']
                    )
                ).first()
                
                if existing:
                    # 更新现有数据
                    for key, value in analysis_data.items():
                        if hasattr(existing, key):
                            setattr(existing, key, value)
                    logger.info(f"✅ 更新店铺 {analysis_data['shop_name']} 日期 {analysis_data['analysis_date']} 的分析数据")
                else:
                    # 创建新数据
                    new_analysis = ShopAnalysis(**analysis_data)
                    session.add(new_analysis)
                    logger.info(f"✅ 新增店铺 {analysis_data['shop_name']} 日期 {analysis_data['analysis_date']} 的分析数据")
                
                session.commit()
                return True
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"❌ 保存店铺分析数据失败: {e}")
                return False
    
    def get_analysis_by_shop_and_date(self, shop_id: int, analysis_date: date) -> Optional[ShopAnalysis]:
        """根据店铺ID和日期获取分析数据
        
        Args:
            shop_id: 店铺ID
            analysis_date: 分析日期
            
        Returns:
            Optional[ShopAnalysis]: 分析数据或None
        """
        with db_manager.get_session_context() as session:
            try:
                analysis = session.query(ShopAnalysis).filter(
                    and_(
                        ShopAnalysis.shop_id == shop_id,
                        ShopAnalysis.analysis_date == analysis_date
                    )
                ).first()
                
                if analysis:
                    # 将对象从Session中分离
                    session.expunge(analysis)
                
                return analysis
                
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺分析数据失败: {e}")
                return None
    
    def get_analysis_by_date_range(self, start_date: date, end_date: date, shop_id: Optional[int] = None) -> List[ShopAnalysis]:
        """根据日期范围获取分析数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            shop_id: 店铺ID，可选
            
        Returns:
            List[ShopAnalysis]: 分析数据列表
        """
        with db_manager.get_session_context() as session:
            try:
                query = session.query(ShopAnalysis).filter(
                    and_(
                        ShopAnalysis.analysis_date >= start_date,
                        ShopAnalysis.analysis_date <= end_date
                    )
                )
                
                if shop_id:
                    query = query.filter(ShopAnalysis.shop_id == shop_id)
                
                analyses = query.order_by(ShopAnalysis.analysis_date.desc()).all()
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return analyses
                
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺分析数据失败: {e}")
                return []
    
    def get_latest_analysis_by_shop(self, shop_id: int, limit: int = 30) -> List[ShopAnalysis]:
        """获取店铺最近的分析数据
        
        Args:
            shop_id: 店铺ID
            limit: 限制数量
            
        Returns:
            List[ShopAnalysis]: 分析数据列表
        """
        with db_manager.get_session_context() as session:
            try:
                analyses = session.query(ShopAnalysis).filter(
                    ShopAnalysis.shop_id == shop_id
                ).order_by(ShopAnalysis.analysis_date.desc()).limit(limit).all()
                
                # 将对象从Session中分离
                session.expunge_all()
                
                return analyses
                
            except SQLAlchemyError as e:
                logger.error(f"❌ 查询店铺分析数据失败: {e}")
                return []
    
    def delete_analysis_data(self, shop_id: int, analysis_date: date) -> bool:
        """删除指定店铺和日期的分析数据
        
        Args:
            shop_id: 店铺ID
            analysis_date: 分析日期
            
        Returns:
            bool: 删除是否成功
        """
        with db_manager.get_session_context() as session:
            try:
                deleted_count = session.query(ShopAnalysis).filter(
                    and_(
                        ShopAnalysis.shop_id == shop_id,
                        ShopAnalysis.analysis_date == analysis_date
                    )
                ).delete()
                
                session.commit()
                
                if deleted_count > 0:
                    logger.info(f"✅ 删除店铺 {shop_id} 日期 {analysis_date} 的分析数据")
                    return True
                else:
                    logger.warning(f"⚠️ 未找到店铺 {shop_id} 日期 {analysis_date} 的分析数据")
                    return False
                    
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"❌ 删除店铺分析数据失败: {e}")
                return False

# 创建全局实例
shop_analysis_dao = ShopAnalysisDAO()