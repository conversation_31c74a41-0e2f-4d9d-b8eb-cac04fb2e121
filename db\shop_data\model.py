from sqlalchemy import Column, Integer, String, Float, BigInteger, Index
from sqlalchemy.ext.declarative import declarative_base
from db.connection import Base

class ShopData(Base):
    __tablename__ = 'shop_data'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    mt_shop_id = Column(String(50), nullable=False, comment='美团店铺ID')
    date = Column(String(10), nullable=False, comment='日期，格式：YYYY-MM-DD')
    platform = Column(Integer, nullable=False, comment='平台：1点评，2美团')
    shop_id = Column(Integer, nullable=False, comment='内部店铺ID')
    shop_name = Column(String(255), nullable=False, comment='店铺名称')
    
    # 评价数据
    review_new_cnt = Column(Integer, default=0, comment='新增评价数量')
    bad_review_new_cnt = Column(Integer, default=0, comment='新增差评数量')
    good_review_new_cnt = Column(Integer, default=0, comment='新增好评数量')
    
    # 交易数据
    order_cnt = Column(Integer, default=0, comment='下单人数')
    order_coupon_cnt = Column(Integer, default=0, comment='下单券数')
    order_original_price = Column(Float, default=0.0, comment='下单金额(原价)')
    order_price = Column(Float, default=0.0, comment='下单金额')
    verify_cnt = Column(Integer, default=0, comment='核销人数')
    verify_coupon_cnt = Column(Integer, default=0, comment='核销劵数')
    verify_original_price = Column(Float, default=0.0, comment='核销金额（原价）')
    verify_price = Column(Float, default=0.0, comment='核销金额')
    refund_cnt = Column(Integer, default=0, comment='退款劵数')
    refund_original_price = Column(Float, default=0.0, comment='退款金额（原价）')
    refund_price = Column(Float, default=0.0, comment='退款金额')
    
    # 曝光数据
    exposure_cnt = Column(Integer, default=0, comment='曝光人数')
    exposure_count_cnt = Column(Integer, default=0, comment='曝光次数')
    exposure_visit_cnt = Column(Integer, default=0, comment='访问人数')
    exposure_visit_count_cnt = Column(Integer, default=0, comment='访问次数')
    exposure_visit_rate = Column(Float, default=0.0, comment='访问率')
    intent_cnt = Column(Integer, default=0, comment='意向转化人数')
    intent_rate = Column(Float, default=0.0, comment='意向转化率')
    leave_cnt = Column(Integer, default=0, comment='留资人数')
    collect_cnt = Column(Integer, default=0, comment='新增收藏人数')
    check_cnt = Column(Integer, default=0, comment='新增打卡人数')
    
    # 星级数据
    star = Column(Float, default=0.0, comment='店铺星级')
    
    # 创建时间
    created_at = Column(BigInteger, nullable=False, comment='创建时间戳')
    updated_at = Column(BigInteger, nullable=False, comment='更新时间戳')
    
    # 创建索引
    __table_args__ = (
        Index('idx_shop_data_mt_shop_id_date_platform', 'mt_shop_id', 'date', 'platform'),
        Index('idx_shop_data_shop_id_date', 'shop_id', 'date'),
        Index('idx_shop_data_date', 'date'),
        Index('idx_shop_data_platform', 'platform'),
    )
    
    @classmethod
    def from_api_data(cls, mt_shop_id: str, date: str, platform: int, shop_id: int, shop_name: str, data: dict):
        """从API数据创建ShopData实例"""
        import time
        current_time = int(time.time() * 1000)
        
        return cls(
            mt_shop_id=mt_shop_id,
            date=date,
            platform=platform,
            shop_id=shop_id,
            shop_name=shop_name,
            review_new_cnt=data.get('review_new_cnt', 0),
            bad_review_new_cnt=data.get('bad_review_new_cnt', 0),
            good_review_new_cnt=data.get('good_review_new_cnt', 0),
            order_cnt=data.get('order_cnt', 0),
            order_coupon_cnt=data.get('order_coupon_cnt', 0),
            order_original_price=data.get('order_original_price', 0.0),
            order_price=data.get('order_price', 0.0),
            verify_cnt=data.get('verify_cnt', 0),
            verify_coupon_cnt=data.get('verify_coupon_cnt', 0),
            verify_original_price=data.get('verify_original_price', 0.0),
            verify_price=data.get('verify_price', 0.0),
            refund_cnt=data.get('refund_cnt', 0),
            refund_original_price=data.get('refund_original_price', 0.0),
            refund_price=data.get('refund_price', 0.0),
            exposure_cnt=data.get('exposure_cnt', 0),
            exposure_count_cnt=data.get('exposure_count_cnt', 0),
            exposure_visit_cnt=data.get('exposure_visit_cnt', 0),
            exposure_visit_count_cnt=data.get('exposure_visit_count_cnt', 0),
            exposure_visit_rate=data.get('exposure_visit_rate', 0.0),
            intent_cnt=data.get('intent_cnt', 0),
            intent_rate=data.get('intent_rate', 0.0),
            leave_cnt=data.get('leave_cnt', 0),
            collect_cnt=data.get('collect_cnt', 0),
            check_cnt=data.get('check_cnt', 0),
            star=data.get('star', 0.0),
            created_at=current_time,
            updated_at=current_time
        )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'mt_shop_id': self.mt_shop_id,
            'date': self.date,
            'platform': self.platform,
            'shop_id': self.shop_id,
            'shop_name': self.shop_name,
            'review_new_cnt': self.review_new_cnt,
            'bad_review_new_cnt': self.bad_review_new_cnt,
            'good_review_new_cnt': self.good_review_new_cnt,
            'order_cnt': self.order_cnt,
            'order_coupon_cnt': self.order_coupon_cnt,
            'order_original_price': self.order_original_price,
            'order_price': self.order_price,
            'verify_cnt': self.verify_cnt,
            'verify_coupon_cnt': self.verify_coupon_cnt,
            'verify_original_price': self.verify_original_price,
            'verify_price': self.verify_price,
            'refund_cnt': self.refund_cnt,
            'refund_original_price': self.refund_original_price,
            'refund_price': self.refund_price,
            'exposure_cnt': self.exposure_cnt,
            'exposure_count_cnt': self.exposure_count_cnt,
            'exposure_visit_cnt': self.exposure_visit_cnt,
            'exposure_visit_count_cnt': self.exposure_visit_count_cnt,
            'exposure_visit_rate': self.exposure_visit_rate,
            'intent_cnt': self.intent_cnt,
            'intent_rate': self.intent_rate,
            'leave_cnt': self.leave_cnt,
            'collect_cnt': self.collect_cnt,
            'check_cnt': self.check_cnt,
            'star': self.star,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }